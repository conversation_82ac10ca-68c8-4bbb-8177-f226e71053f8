#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
提供统一的日志记录功能，支持文件和控制台输出
"""

import logging
import logging.handlers
import os
import yaml
from datetime import datetime
from typing import Optional

class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化日志管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.loggers = {}
        
    def _load_config(self, config_path: str) -> dict:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config.get('logging', {})
        except Exception as e:
            # 如果配置文件不存在或读取失败，使用默认配置
            print(f"警告: 无法读取配置文件 {config_path}: {e}")
            return {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_handler': {'enabled': True, 'max_bytes': 10485760, 'backup_count': 5},
                'console_handler': {'enabled': True}
            }
    
    def get_logger(self, name: str, log_file: Optional[str] = None) -> logging.Logger:
        """
        获取或创建日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 日志文件名（可选）
            
        Returns:
            日志记录器实例
        """
        if name in self.loggers:
            return self.loggers[name]
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # 清除已有的处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(self.config.get('format', 
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        
        # 添加控制台处理器
        if self.config.get('console_handler', {}).get('enabled', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.config.get('file_handler', {}).get('enabled', True):
            log_dir = "logs"
            os.makedirs(log_dir, exist_ok=True)
            
            if log_file is None:
                timestamp = datetime.now().strftime("%Y%m%d")
                log_file = f"{timestamp}_{name}.log"
            
            log_path = os.path.join(log_dir, log_file)
            
            # 使用RotatingFileHandler实现日志轮转
            file_handler = logging.handlers.RotatingFileHandler(
                log_path,
                maxBytes=self.config.get('file_handler', {}).get('max_bytes', 10485760),
                backupCount=self.config.get('file_handler', {}).get('backup_count', 5),
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        self.loggers[name] = logger
        return logger

# 全局日志管理器实例
_logger_manager = None

def get_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """
    获取日志记录器的便捷函数
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件名（可选）
        
    Returns:
        日志记录器实例
    """
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    
    return _logger_manager.get_logger(name, log_file)

def log_operation(operation_name: str, details: str, level: str = "INFO"):
    """
    记录操作日志的便捷函数
    
    Args:
        operation_name: 操作名称
        details: 操作详情
        level: 日志级别
    """
    logger = get_logger("operation")
    log_method = getattr(logger, level.lower(), logger.info)
    log_method(f"[{operation_name}] {details}")

def log_error(error_msg: str, exception: Optional[Exception] = None):
    """
    记录错误日志的便捷函数
    
    Args:
        error_msg: 错误消息
        exception: 异常对象（可选）
    """
    logger = get_logger("error")
    if exception:
        logger.error(f"{error_msg}: {str(exception)}", exc_info=True)
    else:
        logger.error(error_msg)

def log_data_processing(step: str, details: str, record_count: int = 0):
    """
    记录数据处理日志的便捷函数
    
    Args:
        step: 处理步骤
        details: 处理详情
        record_count: 处理记录数
    """
    logger = get_logger("data_processing")
    if record_count > 0:
        logger.info(f"[{step}] {details} - 处理记录数: {record_count}")
    else:
        logger.info(f"[{step}] {details}")

def log_rule_validation(rule_name: str, result: str, details: str):
    """
    记录规则校验日志的便捷函数
    
    Args:
        rule_name: 规则名称
        result: 校验结果（PASS/FAIL）
        details: 校验详情
    """
    logger = get_logger("rule_validation")
    logger.info(f"[{rule_name}] {result} - {details}")

# 测试函数
def test_logger():
    """测试日志系统"""
    print("测试日志系统...")
    
    # 测试不同类型的日志
    log_operation("系统启动", "金豆发放监控系统启动")
    log_data_processing("数据读取", "读取金豆发放申请明细表", 100)
    log_rule_validation("单次发放限额", "PASS", "所有记录均通过单次发放限额校验")
    log_error("测试错误", Exception("这是一个测试异常"))
    
    print("日志测试完成，请检查logs目录下的日志文件")

if __name__ == "__main__":
    test_logger()
