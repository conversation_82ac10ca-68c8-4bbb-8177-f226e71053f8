# 金豆发放监控与分析系统配置文件

# 数据路径配置
data_paths:
  raw_data_dir: "Data/Raw"
  processed_data_dir: "Data/Processed"
  backup_data_dir: "Data/Backup"
  
# 数据文件配置
data_files:
  # 输入文件
  application_detail_pattern: "金豆发放申请明细表"  # 支持模糊匹配
  application_main_detail: "金豆发放申请主明细表.xlsx"
  statistics_table: "金豆发放统计表.xlsx"
  rules_config: "金豆发放规则配置表.xlsx"
  yearly_statistics: "金豆发放年统计表.xlsx"
  
  # 输出文件命名格式
  backup_format: "{timestamp}_{filename}"  # 备份文件格式
  log_format: "{timestamp}_{operation}.log"  # 日志文件格式

# 数据处理配置
data_processing:
  # 日期格式配置
  date_formats:
    input_formats: ["YYYY.MM.DD", "YYYYMMDD", "YYYY-MM-DD"]
    output_format: "YYYY-MM-DD"
  
  # 数据校验配置
  validation:
    phone_pattern: "^[0-9]{11}$"  # 手机号格式
    min_amount: 1  # 最小发放数量
    max_amount: 999999  # 最大发放数量
  
  # 重试配置
  retry:
    max_attempts: 3
    delay_seconds: 1

# 规则校验配置
rule_validation:
  # 规则代码映射
  rule_codes:
    single_limit: "SINGLE_LIMIT"
    yearly_limit: "YEAR_LIMIT"
    lifetime_limit: "CUSTOMER_TOTAL_LIMIT"
  
  # 默认规则值（当配置表不可用时使用）
  default_limits:
    single_limit: 1000
    yearly_limit: 10000
    lifetime_limit: 30000

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_handler:
    enabled: true
    max_bytes: 10485760  # 10MB
    backup_count: 5
  console_handler:
    enabled: true

# Web应用配置
web_app:
  host: "127.0.0.1"
  port: 5000
  debug: false
  
  # 图表配置
  charts:
    default_colors: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"]
    top_n_limit: 10  # Top N排行榜显示数量
    
  # 筛选器配置
  filters:
    date_ranges:
      - name: "本年"
        value: "current_year"
      - name: "本月"
        value: "current_month"
      - name: "近90天"
        value: "last_90_days"
      - name: "自定义"
        value: "custom"

# 数据库配置（预留，当前使用Excel文件）
database:
  enabled: false
  type: "sqlite"  # sqlite, mysql, postgresql
  connection_string: ""

# 性能配置
performance:
  chunk_size: 1000  # 大数据集分块处理大小
  max_memory_usage: "1GB"  # 最大内存使用量
  
# 安全配置
security:
  enable_backup: true  # 是否启用自动备份
  backup_retention_days: 30  # 备份文件保留天数
  
# 扩展点配置
# TODO: 扩展点 - 新增规则类型时在此配置
extensions:
  custom_rules: []  # 自定义规则列表
  custom_validators: []  # 自定义校验器列表
  custom_charts: []  # 自定义图表类型列表

# 错误处理配置
error_handling:
  continue_on_error: false  # 遇到错误是否继续处理
  error_notification:
    enabled: false
    email: ""
    webhook: ""

# 开发配置
development:
  debug_mode: false
  test_data_enabled: false
  mock_data_path: "Data/Test"
