#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据统计与备份模块
实现统计表更新、数据备份、违规记录统计功能，确保数据安全和统计准确性
"""

import pandas as pd
import os
import shutil
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from collections import defaultdict

from src.utils import (
    load_config, 
    backup_file, 
    get_timestamp, 
    extract_year_from_date,
    retry_operation
)
from src.logger import get_logger, log_data_processing, log_error
from src.models import (
    ApplicationRecord, 
    CustomerStatistics, 
    YearlyStatistics,
    ValidationResult,
    DataFrameConverter
)

class StatisticsManager:
    """数据统计与备份管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化统计管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        self.logger = get_logger("statistics_manager")
        
        # 获取配置参数
        self.data_paths = self.config.get('data_paths', {})
        self.data_files = self.config.get('data_files', {})
        
        # 文件路径
        self.raw_data_dir = self.data_paths.get('raw_data_dir', 'Data/Raw')
        self.backup_dir = self.data_paths.get('backup_data_dir', 'Data/Backup')
        
        # 确保备份目录存在
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def backup_tables(self) -> Dict[str, str]:
        """
        备份相关数据表
        
        Returns:
            备份文件路径字典
        """
        backup_results = {}
        
        try:
            # 需要备份的表文件
            tables_to_backup = [
                self.data_files.get('application_main_detail', '金豆发放申请主明细表.xlsx'),
                self.data_files.get('statistics_table', '金豆发放统计表.xlsx'),
                self.data_files.get('yearly_statistics', '金豆发放年统计表.xlsx')
            ]
            
            for table_file in tables_to_backup:
                source_path = os.path.join(self.raw_data_dir, table_file)
                
                if os.path.exists(source_path):
                    backup_path = backup_file(source_path, self.backup_dir)
                    if backup_path:
                        backup_results[table_file] = backup_path
                        log_data_processing("数据备份", f"成功备份表: {table_file}")
                    else:
                        log_error(f"备份表失败: {table_file}")
                else:
                    self.logger.warning(f"表文件不存在，跳过备份: {table_file}")
            
            log_data_processing("批量备份", f"完成 {len(backup_results)} 个表的备份")
            return backup_results
        
        except Exception as e:
            log_error("批量备份失败", e)
            return {}
    
    def load_current_statistics(self) -> Tuple[Dict[str, CustomerStatistics], Dict[str, YearlyStatistics]]:
        """
        加载当前统计数据
        
        Returns:
            (客户统计字典, 年度统计字典)
        """
        try:
            # 加载客户统计表
            customer_stats = {}
            stats_file = os.path.join(self.raw_data_dir, 
                                    self.data_files.get('statistics_table', '金豆发放统计表.xlsx'))
            
            if os.path.exists(stats_file):
                df = retry_operation(
                    func=pd.read_excel,
                    max_attempts=3,
                    delay_seconds=1,
                    args=[stats_file]
                )
                
                if not df.empty:
                    stats_list = DataFrameConverter.to_customer_statistics(df)
                    for stat in stats_list:
                        customer_stats[stat.get_customer_key()] = stat
            
            # 加载年度统计表
            yearly_stats = {}
            yearly_file = os.path.join(self.raw_data_dir,
                                     self.data_files.get('yearly_statistics', '金豆发放年统计表.xlsx'))
            
            if os.path.exists(yearly_file):
                df = retry_operation(
                    func=pd.read_excel,
                    max_attempts=3,
                    delay_seconds=1,
                    args=[yearly_file]
                )
                
                if not df.empty:
                    stats_list = DataFrameConverter.to_yearly_statistics(df)
                    for stat in stats_list:
                        yearly_stats[stat.get_yearly_key()] = stat
            
            log_data_processing("统计加载", f"加载客户统计 {len(customer_stats)} 条，年度统计 {len(yearly_stats)} 条")
            return customer_stats, yearly_stats
        
        except Exception as e:
            log_error("加载统计数据失败", e)
            return {}, {}
    
    def update_customer_statistics(self, records: List[ApplicationRecord],
                                 current_stats: Dict[str, CustomerStatistics]) -> Dict[str, CustomerStatistics]:
        """
        更新客户统计数据（支持机构变更处理）

        Args:
            records: 成功处理的记录列表
            current_stats: 当前客户统计数据

        Returns:
            更新后的客户统计数据
        """
        try:
            updated_stats = current_stats.copy()

            # 按客户聚合数据（使用新的组合主键）
            customer_aggregates = defaultdict(lambda: {'amount': 0, 'count': 0, 'organization': '', 'customer_enterprise': None})

            for record in records:
                # 使用新的客户主键（包含客户归属企业）
                customer_key = record.get_customer_key()
                customer_aggregates[customer_key]['amount'] += record.amount
                customer_aggregates[customer_key]['count'] += 1
                customer_aggregates[customer_key]['organization'] = record.organization
                customer_aggregates[customer_key]['customer_enterprise'] = record.customer_enterprise
                customer_aggregates[customer_key]['customer_name'] = record.customer_name
                customer_aggregates[customer_key]['customer_phone'] = record.customer_phone

            # 更新统计数据
            for customer_key, aggregate in customer_aggregates.items():
                if customer_key in updated_stats:
                    # 更新现有统计
                    stat = updated_stats[customer_key]
                    stat.total_amount += aggregate['amount']
                    stat.total_count += aggregate['count']

                    # 检查并处理机构变更
                    if aggregate['organization'] != stat.organization:
                        self.logger.info(f"检测到客户 {aggregate['customer_name']} 机构变更: {stat.organization} -> {aggregate['organization']}")
                        stat.append_organization_history(aggregate['organization'])
                else:
                    # 创建新统计
                    stat = CustomerStatistics(
                        customer_name=aggregate['customer_name'],
                        customer_phone=aggregate['customer_phone'],
                        customer_enterprise=aggregate['customer_enterprise'],
                        organization=aggregate['organization'],
                        total_amount=aggregate['amount'],
                        total_count=aggregate['count']
                    )
                    updated_stats[customer_key] = stat

            log_data_processing("客户统计更新", f"更新 {len(customer_aggregates)} 个客户的统计数据")
            return updated_stats

        except Exception as e:
            log_error("更新客户统计失败", e)
            return current_stats
    
    def update_yearly_statistics(self, records: List[ApplicationRecord],
                               current_stats: Dict[str, YearlyStatistics]) -> Dict[str, YearlyStatistics]:
        """
        更新年度统计数据（支持机构变更处理）

        Args:
            records: 成功处理的记录列表
            current_stats: 当前年度统计数据

        Returns:
            更新后的年度统计数据
        """
        try:
            updated_stats = current_stats.copy()

            # 按客户和年份聚合数据（使用新的组合主键）
            yearly_aggregates = defaultdict(lambda: {
                'amount': 0, 'count': 0, 'organization': '', 'year': 0,
                'customer_enterprise': None, 'customer_name': '', 'customer_phone': ''
            })

            for record in records:
                year = extract_year_from_date(record.application_date)
                # 使用新的年度主键（包含客户归属企业）
                yearly_key = f"{record.customer_name}_{record.customer_phone}_{year}_{record.customer_enterprise or ''}"

                yearly_aggregates[yearly_key]['amount'] += record.amount
                yearly_aggregates[yearly_key]['count'] += 1
                yearly_aggregates[yearly_key]['organization'] = record.organization
                yearly_aggregates[yearly_key]['year'] = year
                yearly_aggregates[yearly_key]['customer_enterprise'] = record.customer_enterprise
                yearly_aggregates[yearly_key]['customer_name'] = record.customer_name
                yearly_aggregates[yearly_key]['customer_phone'] = record.customer_phone

            # 更新统计数据
            for yearly_key, aggregate in yearly_aggregates.items():
                if yearly_key in updated_stats:
                    # 更新现有统计
                    stat = updated_stats[yearly_key]
                    stat.yearly_amount += aggregate['amount']
                    stat.total_count += aggregate['count']

                    # 检查并处理机构变更
                    if aggregate['organization'] != stat.organization:
                        self.logger.info(f"检测到客户 {aggregate['customer_name']} 年度统计机构变更: {stat.organization} -> {aggregate['organization']}")
                        stat.append_organization_history(aggregate['organization'])
                else:
                    # 创建新统计
                    stat = YearlyStatistics(
                        customer_name=aggregate['customer_name'],
                        customer_phone=aggregate['customer_phone'],
                        customer_enterprise=aggregate['customer_enterprise'],
                        organization=aggregate['organization'],
                        year=aggregate['year'],
                        yearly_amount=aggregate['amount'],
                        total_count=aggregate['count']
                    )
                    updated_stats[yearly_key] = stat

            log_data_processing("年度统计更新", f"更新 {len(yearly_aggregates)} 个客户年度的统计数据")
            return updated_stats
        
        except Exception as e:
            log_error("更新年度统计失败", e)
            return current_stats

    def update_violation_statistics(self, failed_results: List[ValidationResult],
                                   customer_stats: Dict[str, CustomerStatistics],
                                   yearly_stats: Dict[str, YearlyStatistics]) -> Tuple[Dict[str, CustomerStatistics], Dict[str, YearlyStatistics]]:
        """
        更新违规统计数据

        Args:
            failed_results: 校验失败的结果列表
            customer_stats: 客户统计数据
            yearly_stats: 年度统计数据

        Returns:
            (更新后的客户统计数据, 更新后的年度统计数据)
        """
        try:
            updated_customer_stats = customer_stats.copy()
            updated_yearly_stats = yearly_stats.copy()

            for failed_result in failed_results:
                record = failed_result.record
                customer_key = f"{record.customer_name}_{record.customer_phone}"
                year = extract_year_from_date(record.application_date)
                yearly_key = f"{record.customer_name}_{record.customer_phone}_{year}"

                # 分析违规类型
                for rule in failed_result.violated_rules:
                    if rule.rule_code == "SINGLE_LIMIT":
                        # 单次发放超规定次数
                        if customer_key in updated_customer_stats:
                            updated_customer_stats[customer_key].single_violation_count += 1
                        else:
                            # 创建新的客户统计记录
                            stat = CustomerStatistics(
                                customer_name=record.customer_name,
                                customer_phone=record.customer_phone,
                                organization=record.organization,
                                single_violation_count=1
                            )
                            updated_customer_stats[customer_key] = stat

                    elif rule.rule_code == "YEAR_LIMIT":
                        # 年累计超规定次数
                        if yearly_key in updated_yearly_stats:
                            updated_yearly_stats[yearly_key].yearly_violation_count += 1
                        else:
                            # 创建新的年度统计记录
                            stat = YearlyStatistics(
                                customer_name=record.customer_name,
                                customer_phone=record.customer_phone,
                                organization=record.organization,
                                year=year,
                                yearly_violation_count=1
                            )
                            updated_yearly_stats[yearly_key] = stat

                    elif rule.rule_code == "CUSTOMER_TOTAL_LIMIT":
                        # 户累计超规定次数
                        if customer_key in updated_customer_stats:
                            updated_customer_stats[customer_key].lifetime_violation_count += 1
                        else:
                            # 创建新的客户统计记录
                            stat = CustomerStatistics(
                                customer_name=record.customer_name,
                                customer_phone=record.customer_phone,
                                organization=record.organization,
                                lifetime_violation_count=1
                            )
                            updated_customer_stats[customer_key] = stat

            log_data_processing("违规统计更新", f"处理 {len(failed_results)} 条违规记录")
            return updated_customer_stats, updated_yearly_stats

        except Exception as e:
            log_error("更新违规统计失败", e)
            return customer_stats, yearly_stats

    def save_customer_statistics(self, customer_stats: Dict[str, CustomerStatistics]) -> bool:
        """
        保存客户统计数据到Excel文件

        Args:
            customer_stats: 客户统计数据

        Returns:
            是否保存成功
        """
        try:
            if not customer_stats:
                # 创建空的DataFrame
                df = pd.DataFrame(columns=[
                    '记录ID', '客户名称', '客户手机号', '客户归属企业', '等值金豆发放户累计',
                    '等值金豆发放总次数', '申请发放机构', '单次发放超规定次数',
                    '户累计超规定次数', '备注'
                ])
            else:
                # 转换为DataFrame
                data = []
                for i, (key, stat) in enumerate(customer_stats.items(), 1):
                    data.append({
                        '记录ID': i,
                        '客户名称': stat.customer_name,
                        '客户手机号': stat.customer_phone,
                        '客户归属企业': stat.customer_enterprise or '',
                        '等值金豆发放户累计': stat.total_amount,
                        '等值金豆发放总次数': stat.total_count,
                        '申请发放机构': stat.organization,
                        '单次发放超规定次数': stat.single_violation_count,
                        '户累计超规定次数': stat.lifetime_violation_count,
                        '备注': stat.remark or ''
                    })

                df = pd.DataFrame(data)

            # 保存到文件
            output_path = os.path.join(self.raw_data_dir,
                                     self.data_files.get('statistics_table', '金豆发放统计表.xlsx'))

            retry_operation(
                func=df.to_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[output_path],
                kwargs={'index': False}
            )

            log_data_processing("客户统计保存", f"成功保存 {len(customer_stats)} 条客户统计记录")
            return True

        except Exception as e:
            log_error("保存客户统计失败", e)
            return False

    def save_yearly_statistics(self, yearly_stats: Dict[str, YearlyStatistics]) -> bool:
        """
        保存年度统计数据到Excel文件

        Args:
            yearly_stats: 年度统计数据

        Returns:
            是否保存成功
        """
        try:
            if not yearly_stats:
                # 创建空的DataFrame
                df = pd.DataFrame(columns=[
                    '记录ID', '客户名称', '客户手机号', '客户归属企业', '申请发放机构',
                    '发放年份', '等值金豆发放总次数', '等值金豆发放年累计',
                    '年累计超规定次数', '备注'
                ])
            else:
                # 转换为DataFrame
                data = []
                for i, (key, stat) in enumerate(yearly_stats.items(), 1):
                    data.append({
                        '记录ID': i,
                        '客户名称': stat.customer_name,
                        '客户手机号': stat.customer_phone,
                        '客户归属企业': stat.customer_enterprise or '',
                        '申请发放机构': stat.organization,
                        '发放年份': stat.year,
                        '等值金豆发放总次数': stat.total_count,
                        '等值金豆发放年累计': stat.yearly_amount,
                        '年累计超规定次数': stat.yearly_violation_count,
                        '备注': stat.remark or ''
                    })

                df = pd.DataFrame(data)

            # 保存到文件
            output_path = os.path.join(self.raw_data_dir,
                                     self.data_files.get('yearly_statistics', '金豆发放年统计表.xlsx'))

            retry_operation(
                func=df.to_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[output_path],
                kwargs={'index': False}
            )

            log_data_processing("年度统计保存", f"成功保存 {len(yearly_stats)} 条年度统计记录")
            return True

        except Exception as e:
            log_error("保存年度统计失败", e)
            return False

    def append_to_main_detail(self, records: List[ApplicationRecord]) -> bool:
        """
        将记录追加到主明细表

        Args:
            records: 要追加的记录列表

        Returns:
            是否追加成功
        """
        try:
            if not records:
                log_data_processing("主明细追加", "没有记录需要追加")
                return True

            # 读取现有主明细表
            main_detail_path = os.path.join(self.raw_data_dir,
                                          self.data_files.get('application_main_detail', '金豆发放申请主明细表.xlsx'))

            if os.path.exists(main_detail_path):
                existing_df = retry_operation(
                    func=pd.read_excel,
                    max_attempts=3,
                    delay_seconds=1,
                    args=[main_detail_path]
                )
            else:
                # 创建空的DataFrame（使用新的字段结构）
                existing_df = pd.DataFrame(columns=[
                    '记录ID', '客户名称', '客户手机号', '客户归属企业', '等值金豆发放数量',
                    '申请发放机构', '申请发放日期', '有效时间（天）', '活动方案全称',
                    '归属条线', '申请填报日期', '备注（非必填）'
                ])

            # 准备新记录数据
            new_data = []
            start_id = len(existing_df) + 1

            for i, record in enumerate(records):
                # 将日期转换为整数格式（YYYYMMDD）
                application_date_str = record.application_date.replace('-', '')
                application_date_int = int(application_date_str) if application_date_str.isdigit() else 0

                report_date_str = record.report_date.replace('-', '')
                report_date_int = int(report_date_str) if report_date_str.isdigit() else 0

                new_data.append({
                    '记录ID': start_id + i,
                    '客户名称': record.customer_name,
                    '客户手机号': int(record.customer_phone),
                    '客户归属企业': record.customer_enterprise or '',
                    '等值金豆发放数量': record.amount,
                    '申请发放机构': record.organization,
                    '申请发放日期': application_date_int,
                    '有效时间（天）': record.valid_days,
                    '活动方案全称': record.activity_name,
                    '归属条线': record.business_line,
                    '申请填报日期': report_date_int,
                    '备注（非必填）': record.remark or ''
                })

            # 创建新记录DataFrame
            new_df = pd.DataFrame(new_data)

            # 合并数据
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)

            # 保存到文件
            retry_operation(
                func=combined_df.to_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[main_detail_path],
                kwargs={'index': False}
            )

            log_data_processing("主明细追加", f"成功追加 {len(records)} 条记录到主明细表")
            return True

        except Exception as e:
            log_error("追加主明细表失败", e)
            return False

# 测试函数
def test_statistics_manager():
    """测试统计管理器"""
    print("=== 测试统计管理器 ===")

    manager = StatisticsManager()

    # 测试备份功能
    print("\n1. 测试数据备份")
    backup_results = manager.backup_tables()
    print(f"备份结果: {list(backup_results.keys())}")

    # 测试加载统计数据
    print("\n2. 测试加载统计数据")
    customer_stats, yearly_stats = manager.load_current_statistics()
    print(f"客户统计: {len(customer_stats)} 条")
    print(f"年度统计: {len(yearly_stats)} 条")

    # 创建测试记录
    test_records = [
        ApplicationRecord(
            record_id=1,
            customer_name="测试客户1",
            customer_phone="13800138001",
            amount=500,
            organization="测试机构",
            application_date="2025-01-15"
        )
    ]

    # 测试更新统计
    print("\n3. 测试更新统计")
    updated_customer_stats = manager.update_customer_statistics(test_records, customer_stats)
    updated_yearly_stats = manager.update_yearly_statistics(test_records, yearly_stats)

    print(f"更新后客户统计: {len(updated_customer_stats)} 条")
    print(f"更新后年度统计: {len(updated_yearly_stats)} 条")

    print("\n=== 统计管理器测试完成 ===")

if __name__ == "__main__":
    test_statistics_manager()
