#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型模块
定义金豆发放系统中的数据结构和业务实体
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd

@dataclass
class ApplicationRecord:
    """金豆发放申请记录"""
    record_id: int
    customer_name: str
    customer_phone: str
    customer_enterprise: Optional[str]  # 客户归属企业（条件必填）
    amount: int  # 等值金豆发放数量
    organization: str  # 申请发放机构
    application_date: str  # 申请发放日期
    valid_days: int  # 有效时间（天）
    activity_name: str  # 活动方案全称
    business_line: str  # 归属条线
    report_date: str  # 申请填报日期
    remark: Optional[str] = None  # 备注（非必填）

    def get_business_key(self) -> str:
        """获取业务主键（根据新的去重规则）"""
        enterprise = self.customer_enterprise or ""
        return f"{self.customer_name}_{self.customer_phone}_{self.application_date}_{enterprise}_{self.organization}_{self.amount}"

    def get_customer_key(self) -> str:
        """获取客户主键（用于统计表组合主键）"""
        enterprise = self.customer_enterprise or ""
        return f"{self.customer_name}_{self.customer_phone}_{enterprise}"

    def get_year(self) -> int:
        """获取申请年份"""
        try:
            if len(self.application_date) >= 4:
                return int(self.application_date[:4])
            return datetime.now().year
        except:
            return datetime.now().year

    def validate_enterprise_requirement(self) -> bool:
        """验证客户归属企业的条件必填逻辑"""
        # 仅在归属条线为"公司"时，客户归属企业为必填字段
        if self.business_line == "公司":
            return self.customer_enterprise is not None and self.customer_enterprise.strip() != ""
        return True  # 其他归属条线情况下可为空

@dataclass
class CustomerStatistics:
    """客户统计信息"""
    customer_name: str
    customer_phone: str
    customer_enterprise: Optional[str]  # 客户归属企业（条件必填）
    organization: str  # 申请发放机构（支持机构变更历史记录）
    total_amount: int = 0  # 等值金豆发放户累计
    total_count: int = 0   # 等值金豆发放总次数
    single_violation_count: int = 0  # 单次发放超规定次数
    lifetime_violation_count: int = 0  # 户累计超规定次数
    remark: Optional[str] = None

    def get_customer_key(self) -> str:
        """获取客户主键（组合主键）"""
        enterprise = self.customer_enterprise or ""
        return f"{self.customer_name}_{self.customer_phone}_{enterprise}"

    def append_organization_history(self, new_organization: str) -> None:
        """
        累积机构历史记录

        支持客户在多个申请发放机构有业务的场景，将新机构追加到现有机构列表中
        使用分号分隔符连接多个机构，避免重复添加相同机构

        Args:
            new_organization: 新的申请发放机构
        """
        if not new_organization or not new_organization.strip():
            return

        new_organization = new_organization.strip()

        # 如果当前没有机构记录，直接设置
        if not self.organization or not self.organization.strip():
            self.organization = new_organization
            return

        # 解析现有机构列表
        existing_orgs = [org.strip() for org in self.organization.split('；') if org.strip()]

        # 检查是否已存在该机构，避免重复
        if new_organization not in existing_orgs:
            existing_orgs.append(new_organization)
            # 使用分号连接多个机构
            self.organization = '；'.join(existing_orgs)

@dataclass
class YearlyStatistics:
    """年度统计信息"""
    customer_name: str
    customer_phone: str
    customer_enterprise: Optional[str]  # 客户归属企业（条件必填）
    organization: str  # 申请发放机构（支持机构变更历史记录）
    year: int  # 发放年份
    total_count: int = 0   # 等值金豆发放总次数
    yearly_amount: int = 0  # 等值金豆发放年累计
    yearly_violation_count: int = 0  # 年累计超规定次数
    remark: Optional[str] = None

    def get_yearly_key(self) -> str:
        """获取年度主键（组合主键）"""
        enterprise = self.customer_enterprise or ""
        return f"{self.customer_name}_{self.customer_phone}_{self.year}_{enterprise}"

    def append_organization_history(self, new_organization: str) -> None:
        """
        累积机构历史记录

        支持客户在多个申请发放机构有业务的场景，将新机构追加到现有机构列表中
        使用分号分隔符连接多个机构，避免重复添加相同机构

        Args:
            new_organization: 新的申请发放机构
        """
        if not new_organization or not new_organization.strip():
            return

        new_organization = new_organization.strip()

        # 如果当前没有机构记录，直接设置
        if not self.organization or not self.organization.strip():
            self.organization = new_organization
            return

        # 解析现有机构列表
        existing_orgs = [org.strip() for org in self.organization.split('；') if org.strip()]

        # 检查是否已存在该机构，避免重复
        if new_organization not in existing_orgs:
            existing_orgs.append(new_organization)
            # 使用分号连接多个机构
            self.organization = '；'.join(existing_orgs)

@dataclass
class ValidationRule:
    """校验规则"""
    rule_id: int
    rule_code: str
    rule_name: str
    limit_value: int
    period_unit: str
    is_active: bool
    description: str
    last_update: str

@dataclass
class ValidationResult:
    """校验结果"""
    record: ApplicationRecord
    is_valid: bool
    violated_rules: List[ValidationRule]
    error_messages: List[str]
    
    def get_violation_summary(self) -> str:
        """获取违规摘要"""
        if self.is_valid:
            return "校验通过"
        
        rule_names = [rule.rule_name for rule in self.violated_rules]
        return f"违反规则: {', '.join(rule_names)}"

@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    processed_count: int
    failed_count: int
    success_records: List[ApplicationRecord]
    failed_results: List[ValidationResult]
    error_message: Optional[str] = None
    
    def get_summary(self) -> str:
        """获取处理摘要"""
        if self.success:
            return f"处理成功: 共处理 {self.processed_count} 条记录"
        else:
            return f"处理失败: 成功 {self.processed_count} 条，失败 {self.failed_count} 条"

class DataFrameConverter:
    """DataFrame转换器"""
    
    @staticmethod
    def to_application_records(df: pd.DataFrame) -> List[ApplicationRecord]:
        """
        将DataFrame转换为ApplicationRecord列表

        Args:
            df: 包含申请记录的DataFrame

        Returns:
            ApplicationRecord列表
        """
        records = []
        for _, row in df.iterrows():
            try:
                # 处理客户归属企业的条件必填逻辑
                customer_enterprise = row.get('客户归属企业')
                if pd.isna(customer_enterprise) or str(customer_enterprise).strip() == '':
                    customer_enterprise = None
                else:
                    customer_enterprise = str(customer_enterprise).strip()

                record = ApplicationRecord(
                    record_id=int(row.get('记录ID', 0)),
                    customer_name=str(row.get('客户名称', '')).strip(),
                    customer_phone=str(row.get('客户手机号', '')).strip(),
                    customer_enterprise=customer_enterprise,
                    amount=int(row.get('等值金豆发放数量', 0)),
                    organization=str(row.get('申请发放机构', '')).strip(),
                    application_date=str(row.get('申请发放日期', '')).strip(),
                    valid_days=int(row.get('有效时间（天）', 0)),
                    activity_name=str(row.get('活动方案全称', '')).strip(),
                    business_line=str(row.get('归属条线', '')).strip(),
                    report_date=str(row.get('申请填报日期', '')).strip(),
                    remark=row.get('备注（非必填）')
                )
                records.append(record)
            except Exception as e:
                print(f"转换记录失败: {e}, 行数据: {row.to_dict()}")

        return records
    
    @staticmethod
    def to_customer_statistics(df: pd.DataFrame) -> List[CustomerStatistics]:
        """
        将DataFrame转换为CustomerStatistics列表

        Args:
            df: 包含客户统计的DataFrame

        Returns:
            CustomerStatistics列表
        """
        statistics = []
        for _, row in df.iterrows():
            try:
                # 处理客户归属企业的条件必填逻辑
                customer_enterprise = row.get('客户归属企业')
                if pd.isna(customer_enterprise) or str(customer_enterprise).strip() == '':
                    customer_enterprise = None
                else:
                    customer_enterprise = str(customer_enterprise).strip()

                stat = CustomerStatistics(
                    customer_name=str(row.get('客户名称', '')).strip(),
                    customer_phone=str(row.get('客户手机号', '')).strip(),
                    customer_enterprise=customer_enterprise,
                    organization=str(row.get('申请发放机构', '')).strip(),
                    total_amount=int(row.get('等值金豆发放户累计', 0)),
                    total_count=int(row.get('等值金豆发放总次数', 0)),
                    single_violation_count=int(row.get('单次发放超规定次数', 0)),
                    lifetime_violation_count=int(row.get('户累计超规定次数', 0)),
                    remark=row.get('备注')
                )
                statistics.append(stat)
            except Exception as e:
                print(f"转换客户统计失败: {e}, 行数据: {row.to_dict()}")

        return statistics
    
    @staticmethod
    def to_yearly_statistics(df: pd.DataFrame) -> List[YearlyStatistics]:
        """
        将DataFrame转换为YearlyStatistics列表

        Args:
            df: 包含年度统计的DataFrame

        Returns:
            YearlyStatistics列表
        """
        statistics = []
        for _, row in df.iterrows():
            try:
                # 处理客户归属企业的条件必填逻辑
                customer_enterprise = row.get('客户归属企业')
                if pd.isna(customer_enterprise) or str(customer_enterprise).strip() == '':
                    customer_enterprise = None
                else:
                    customer_enterprise = str(customer_enterprise).strip()

                stat = YearlyStatistics(
                    customer_name=str(row.get('客户名称', '')).strip(),
                    customer_phone=str(row.get('客户手机号', '')).strip(),
                    customer_enterprise=customer_enterprise,
                    organization=str(row.get('申请发放机构', '')).strip(),
                    year=int(row.get('发放年份', datetime.now().year)),
                    total_count=int(row.get('等值金豆发放总次数', 0)),
                    yearly_amount=int(row.get('等值金豆发放年累计', 0)),
                    yearly_violation_count=int(row.get('年累计超规定次数', 0)),
                    remark=row.get('备注')
                )
                statistics.append(stat)
            except Exception as e:
                print(f"转换年度统计失败: {e}, 行数据: {row.to_dict()}")

        return statistics
    
    @staticmethod
    def to_validation_rules(df: pd.DataFrame) -> List[ValidationRule]:
        """
        将DataFrame转换为ValidationRule列表
        
        Args:
            df: 包含规则配置的DataFrame
            
        Returns:
            ValidationRule列表
        """
        rules = []
        for _, row in df.iterrows():
            try:
                rule = ValidationRule(
                    rule_id=int(row.get('规则ID', 0)),
                    rule_code=str(row.get('规则代码', '')).strip(),
                    rule_name=str(row.get('规则名称', '')).strip(),
                    limit_value=int(row.get('限制数值', 0)),
                    period_unit=str(row.get('周期单位', '')).strip(),
                    is_active=str(row.get('是否生效', '')).strip() == '是',
                    description=str(row.get('规则描述', '')).strip(),
                    last_update=str(row.get('最后更新时间', '')).strip()
                )
                rules.append(rule)
            except Exception as e:
                print(f"转换规则配置失败: {e}, 行数据: {row.to_dict()}")
        
        return rules

# 注意：测试函数已移除，避免干扰生产数据处理流程
# 如需测试数据模型，请在独立的测试文件中进行
