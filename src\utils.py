#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
提供通用的工具函数，包括文件操作、日期处理、数据验证等
"""

import os
import re
import yaml
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union
import shutil
import glob
from src.logger import get_logger, log_error

# 初始化日志记录器
logger = get_logger("utils")

def load_config(config_path: str = "config.yaml") -> dict:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        log_error(f"加载配置文件失败: {config_path}", e)
        # 返回默认配置
        return {}

def get_timestamp() -> str:
    """
    获取当前时间戳字符串，格式为YYYYMMDD_HHMMSS
    
    Returns:
        时间戳字符串
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def backup_file(file_path: str, backup_dir: str) -> str:
    """
    备份文件
    
    Args:
        file_path: 源文件路径
        backup_dir: 备份目录
        
    Returns:
        备份文件路径
    """
    try:
        # 确保备份目录存在
        os.makedirs(backup_dir, exist_ok=True)
        
        # 生成备份文件名
        file_name = os.path.basename(file_path)
        timestamp = get_timestamp()
        backup_file_name = f"{timestamp}_{file_name}"
        backup_path = os.path.join(backup_dir, backup_file_name)
        
        # 复制文件
        shutil.copy2(file_path, backup_path)
        logger.info(f"文件备份成功: {file_path} -> {backup_path}")
        
        return backup_path
    except Exception as e:
        log_error(f"文件备份失败: {file_path}", e)
        return ""

def find_files_by_pattern(directory: str, pattern: str) -> List[str]:
    """
    根据模式查找文件
    
    Args:
        directory: 目录路径
        pattern: 文件名模式
        
    Returns:
        匹配的文件路径列表
    """
    try:
        # 构建搜索模式
        search_pattern = os.path.join(directory, f"*{pattern}*")
        
        # 查找匹配的文件
        matching_files = glob.glob(search_pattern)
        
        logger.info(f"在目录 {directory} 中找到 {len(matching_files)} 个匹配 '{pattern}' 的文件")
        return matching_files
    except Exception as e:
        log_error(f"查找文件失败: {directory}/{pattern}", e)
        return []

def standardize_date(date_value: Any) -> str:
    """
    将各种格式的日期标准化为YYYY-MM-DD格式
    
    Args:
        date_value: 日期值，可以是字符串、整数或日期对象
        
    Returns:
        标准化的日期字符串，格式为YYYY-MM-DD
    """
    try:
        if pd.isna(date_value):
            return ""
        
        # 如果是字符串类型
        if isinstance(date_value, str):
            # 处理"YYYY.MM.DD"格式
            if re.match(r'^\d{4}\.\d{2}\.\d{2}$', date_value):
                return date_value.replace('.', '-')
            
            # 处理其他可能的字符串格式
            try:
                dt = pd.to_datetime(date_value)
                return dt.strftime('%Y-%m-%d')
            except:
                pass
        
        # 如果是整数类型（如20250101）
        if isinstance(date_value, (int, np.integer)):
            date_str = str(date_value)
            if len(date_str) == 8:
                return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        
        # 尝试使用pandas转换
        try:
            dt = pd.to_datetime(date_value)
            return dt.strftime('%Y-%m-%d')
        except:
            logger.warning(f"无法标准化日期值: {date_value}，类型: {type(date_value)}")
            return str(date_value)
    
    except Exception as e:
        log_error(f"日期标准化失败: {date_value}", e)
        return str(date_value)

def extract_year_from_date(date_value: Any) -> int:
    """
    从日期中提取年份
    
    Args:
        date_value: 日期值，可以是字符串、整数或日期对象
        
    Returns:
        年份整数
    """
    try:
        # 先标准化日期
        std_date = standardize_date(date_value)
        
        # 从标准化日期中提取年份
        if std_date and len(std_date) >= 4:
            year_str = std_date[:4]
            if year_str.isdigit():
                return int(year_str)
        
        # 如果是整数类型（如20250101）
        if isinstance(date_value, (int, np.integer)):
            date_str = str(date_value)
            if len(date_str) >= 4:
                year_str = date_str[:4]
                if year_str.isdigit():
                    return int(year_str)
        
        # 当前年份作为默认值
        logger.warning(f"无法从日期值提取年份: {date_value}，使用当前年份")
        return datetime.now().year
    
    except Exception as e:
        log_error(f"年份提取失败: {date_value}", e)
        return datetime.now().year

def validate_phone_number(phone: Any) -> bool:
    """
    验证手机号格式是否正确（11位数字）
    
    Args:
        phone: 手机号
        
    Returns:
        是否有效
    """
    try:
        if pd.isna(phone):
            return False
        
        phone_str = str(phone).strip()
        return bool(re.match(r'^[0-9]{11}$', phone_str))
    except Exception as e:
        log_error(f"手机号验证失败: {phone}", e)
        return False

def validate_amount(amount: Any) -> bool:
    """
    验证金额是否为有效的正数
    
    Args:
        amount: 金额值
        
    Returns:
        是否有效
    """
    try:
        if pd.isna(amount):
            return False
        
        # 尝试转换为浮点数
        amount_float = float(amount)
        return amount_float > 0
    except Exception as e:
        log_error(f"金额验证失败: {amount}", e)
        return False

def retry_operation(func, max_attempts: int = 3, delay_seconds: int = 1, args=None, kwargs=None):
    """
    带重试机制的操作执行函数

    Args:
        func: 要执行的函数
        max_attempts: 最大尝试次数
        delay_seconds: 重试延迟秒数
        args: 传递给func的位置参数列表
        kwargs: 传递给func的关键字参数字典

    Returns:
        函数执行结果
    """
    import time

    if args is None:
        args = []
    if kwargs is None:
        kwargs = {}

    attempt = 0
    last_exception = None

    while attempt < max_attempts:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            attempt += 1
            last_exception = e
            logger.warning(f"操作失败，尝试 {attempt}/{max_attempts}: {str(e)}")

            if attempt < max_attempts:
                time.sleep(delay_seconds)

    # 所有尝试都失败
    log_error(f"操作在 {max_attempts} 次尝试后失败", last_exception)
    raise last_exception

def clean_string(text: Any) -> str:
    """
    清理字符串（去除前后空格）
    
    Args:
        text: 输入文本
        
    Returns:
        清理后的字符串
    """
    if pd.isna(text):
        return ""
    
    return str(text).strip()

# 测试函数
def test_utils():
    """测试工具函数"""
    print("测试工具函数...")
    
    # 测试日期标准化
    print(f"日期标准化测试:")
    test_dates = [
        "2025.05.11", 
        20250511, 
        "2025-05-11", 
        datetime(2025, 5, 11)
    ]
    for date in test_dates:
        print(f"  {date} -> {standardize_date(date)}")
    
    # 测试年份提取
    print(f"\n年份提取测试:")
    for date in test_dates:
        print(f"  {date} -> {extract_year_from_date(date)}")
    
    # 测试手机号验证
    print(f"\n手机号验证测试:")
    test_phones = ["13800138000", "1380013800", 13800138000, "abc"]
    for phone in test_phones:
        print(f"  {phone} -> {validate_phone_number(phone)}")
    
    # 测试金额验证
    print(f"\n金额验证测试:")
    test_amounts = [100, 0, -10, "100", "abc"]
    for amount in test_amounts:
        print(f"  {amount} -> {validate_amount(amount)}")
    
    print("\n工具函数测试完成")

if __name__ == "__main__":
    test_utils()
