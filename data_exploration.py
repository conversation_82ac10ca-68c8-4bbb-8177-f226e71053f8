#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金豆发放数据探索分析脚本
用于分析Data/Raw目录下所有数据表的结构、字段类型、数据质量
"""

import pandas as pd
import os
import sys
from datetime import datetime

def analyze_dataframe(df, table_name):
    """
    分析DataFrame的基本信息
    
    Args:
        df: pandas DataFrame
        table_name: 表名
    """
    print(f"\n{'='*50}")
    print(f"表名: {table_name}")
    print(f"{'='*50}")
    
    # 基本信息
    print(f"数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")
    print(f"列名: {list(df.columns)}")
    
    # 数据类型
    print(f"\n数据类型:")
    for col, dtype in df.dtypes.items():
        print(f"  {col}: {dtype}")
    
    # 缺失值统计
    print(f"\n缺失值统计:")
    missing_stats = df.isnull().sum()
    for col, missing_count in missing_stats.items():
        missing_pct = (missing_count / len(df)) * 100
        print(f"  {col}: {missing_count} ({missing_pct:.2f}%)")
    
    # 前5行数据
    print(f"\n前5行数据:")
    print(df.head().to_string())
    
    # 数值型字段统计
    numeric_cols = df.select_dtypes(include=['number']).columns
    if len(numeric_cols) > 0:
        print(f"\n数值型字段统计:")
        print(df[numeric_cols].describe().to_string())
    
    # 文本型字段统计
    text_cols = df.select_dtypes(include=['object']).columns
    if len(text_cols) > 0:
        print(f"\n文本型字段统计:")
        for col in text_cols:
            unique_count = df[col].nunique()
            print(f"  {col}: {unique_count} 个唯一值")
            if unique_count <= 10:
                print(f"    唯一值: {list(df[col].unique())}")
    
    return df

def main():
    """主函数"""
    print("金豆发放数据探索分析")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 数据文件列表
    data_files = [
        "金豆发放申请明细表.xlsx",
        "金豆发放申请主明细表.xlsx", 
        "金豆发放统计表.xlsx",
        "金豆发放规则配置表.xlsx",
        "金豆发放年统计表.xlsx"
    ]
    
    data_dir = "Data/Raw"
    
    # 检查数据目录
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录 {data_dir} 不存在")
        return
    
    # 分析每个数据文件
    for file_name in data_files:
        file_path = os.path.join(data_dir, file_name)
        
        if os.path.exists(file_path):
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path)
                analyze_dataframe(df, file_name)
                
            except Exception as e:
                print(f"\n错误: 无法读取文件 {file_name}")
                print(f"错误信息: {str(e)}")
        else:
            print(f"\n警告: 文件 {file_name} 不存在")
    
    print(f"\n{'='*50}")
    print("数据探索分析完成")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
