#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金豆发放监控与分析系统 - 主程序
整合数据处理、规则校验、统计更新等所有功能模块
"""

import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加当前目录到Python路径
sys.path.append('.')

from src.utils import load_config, get_timestamp
from src.logger import get_logger, log_operation, log_error, log_data_processing
from src.data_processor import DataProcessor
from src.rule_validator import RuleValidator
from src.statistics_manager import StatisticsManager
from src.models import ApplicationRecord, ProcessingResult
from src.console_output import (
    print_banner, print_section, print_step, print_module_action,
    print_result, print_summary, print_timestamp,
    print_conflict_detection_result, print_limit_validation_result
)

class GoldBeanProcessor:
    """金豆发放处理主程序"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化主程序
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        self.logger = get_logger("main_processor")
        
        # 初始化各个模块
        self.data_processor = DataProcessor(config_path)
        self.rule_validator = RuleValidator(config_path)
        self.statistics_manager = StatisticsManager(config_path)
        
        log_operation("系统初始化", "金豆发放监控与分析系统启动")
        print_module_action("SYSTEM", "系统初始化完成", "SUCCESS", "所有核心模块已加载")
    
    def process_application_details(self) -> ProcessingResult:
        """
        处理金豆发放申请明细表的完整流程
        
        Returns:
            处理结果
        """
        try:
            print_section("金豆发放申请明细表处理流程")
            log_operation("开始处理", "启动金豆发放申请明细表处理流程")

            # Step 1: 数据处理 - 读取、清洗、去重
            print_step(1, "数据处理 - 读取、清洗、去重", 5)
            print_module_action("DATA_PROCESSOR", "开始数据处理")
            log_data_processing("步骤1", "开始数据处理")
            records, data_errors = self.data_processor.process_application_details()
            
            if not records:
                error_msg = "没有可处理的数据记录"
                if data_errors:
                    error_msg += f"，错误信息：{'; '.join(data_errors)}"

                print_module_action("DATA_PROCESSOR", "数据处理完成", "ERROR", error_msg)
                log_error(error_msg)
                return ProcessingResult(
                    success=False,
                    processed_count=0,
                    failed_count=0,
                    success_records=[],
                    failed_results=[],
                    error_message=error_msg
                )
            
            print_module_action("DATA_PROCESSOR", "数据处理完成", "SUCCESS", f"获得 {len(records)} 条有效记录")
            log_data_processing("步骤1", f"数据处理完成，获得 {len(records)} 条有效记录")

            # Step 2: 规则校验
            print_step(2, "规则校验 - 业务规则检查", 5)
            print_module_action("RULE_VALIDATOR", "开始规则校验")
            log_data_processing("步骤2", "开始规则校验")
            validation_result = self.rule_validator.validate_batch(records)
            
            if not validation_result.success:
                # 校验失败，更新违规统计
                print_module_action("RULE_VALIDATOR", "规则校验失败", "WARNING", "发现违规记录，开始更新违规统计")
                log_data_processing("步骤2", "规则校验失败，更新违规统计")

                # 使用优化的控制台输出格式显示限额校验结果
                print_limit_validation_result(validation_result.success, validation_result.failed_results)

                # 备份数据表
                print_module_action("STATISTICS_MANAGER", "开始数据备份")
                backup_results = self.statistics_manager.backup_tables()
                print_module_action("STATISTICS_MANAGER", "数据备份完成", "SUCCESS", f"备份 {len(backup_results)} 个数据表")
                log_data_processing("数据备份", f"备份 {len(backup_results)} 个数据表")
                
                # 加载当前统计数据
                customer_stats, yearly_stats = self.statistics_manager.load_current_statistics()
                
                # 更新违规统计
                updated_customer_stats, updated_yearly_stats = self.statistics_manager.update_violation_statistics(
                    validation_result.failed_results, customer_stats, yearly_stats
                )
                
                # 保存更新后的统计数据
                customer_save_success = self.statistics_manager.save_customer_statistics(updated_customer_stats)
                yearly_save_success = self.statistics_manager.save_yearly_statistics(updated_yearly_stats)

                if customer_save_success and yearly_save_success:
                    print_module_action("STATISTICS_MANAGER", "违规统计更新完成", "SUCCESS")
                    log_data_processing("违规统计", "违规统计更新完成")
                else:
                    print_module_action("STATISTICS_MANAGER", "违规统计保存失败", "ERROR")
                    log_error("违规统计保存失败")

                return validation_result
            
            print_module_action("RULE_VALIDATOR", "规则校验通过", "SUCCESS", f"{len(validation_result.success_records)} 条记录有效")
            log_data_processing("步骤2", f"规则校验通过，{len(validation_result.success_records)} 条记录有效")

            # Step 3: 数据备份
            print_step(3, "数据备份 - 保护现有数据", 5)
            print_module_action("STATISTICS_MANAGER", "开始数据备份")
            log_data_processing("步骤3", "开始数据备份")
            backup_results = self.statistics_manager.backup_tables()
            print_module_action("STATISTICS_MANAGER", "数据备份完成", "SUCCESS", f"备份 {len(backup_results)} 个表")
            log_data_processing("步骤3", f"数据备份完成，备份 {len(backup_results)} 个表")
            
            # Step 4: 更新统计数据
            print_step(4, "统计更新 - 更新客户和年度统计", 5)
            print_module_action("STATISTICS_MANAGER", "开始更新统计数据")
            log_data_processing("步骤4", "开始更新统计数据")

            # 加载当前统计数据
            customer_stats, yearly_stats = self.statistics_manager.load_current_statistics()

            # 更新客户统计和年度统计
            updated_customer_stats = self.statistics_manager.update_customer_statistics(
                validation_result.success_records, customer_stats
            )
            updated_yearly_stats = self.statistics_manager.update_yearly_statistics(
                validation_result.success_records, yearly_stats
            )
            
            # 保存更新后的统计数据
            customer_save_success = self.statistics_manager.save_customer_statistics(updated_customer_stats)
            yearly_save_success = self.statistics_manager.save_yearly_statistics(updated_yearly_stats)

            if not (customer_save_success and yearly_save_success):
                error_msg = "统计数据保存失败"
                print_module_action("STATISTICS_MANAGER", "统计数据保存失败", "ERROR", error_msg)
                log_error(error_msg)
                return ProcessingResult(
                    success=False,
                    processed_count=0,
                    failed_count=len(records),
                    success_records=[],
                    failed_results=[],
                    error_message=error_msg
                )

            print_module_action("STATISTICS_MANAGER", "统计数据更新完成", "SUCCESS")
            log_data_processing("步骤4", "统计数据更新完成")
            
            # Step 5: 追加到主明细表
            print_step(5, "数据追加 - 更新主明细表", 5)
            print_module_action("STATISTICS_MANAGER", "开始追加主明细表")
            log_data_processing("步骤5", "开始追加主明细表")
            append_success = self.statistics_manager.append_to_main_detail(validation_result.success_records)

            if not append_success:
                error_msg = "主明细表追加失败"
                print_module_action("STATISTICS_MANAGER", "主明细表追加失败", "ERROR", error_msg)
                log_error(error_msg)
                return ProcessingResult(
                    success=False,
                    processed_count=0,
                    failed_count=len(records),
                    success_records=[],
                    failed_results=[],
                    error_message=error_msg
                )

            print_module_action("STATISTICS_MANAGER", "主明细表追加完成", "SUCCESS")
            log_data_processing("步骤5", "主明细表追加完成")

            # 处理成功
            print_module_action("SYSTEM", "数据处理流程完成", "SUCCESS", f"成功处理 {len(validation_result.success_records)} 条记录")
            log_operation("处理完成", f"成功处理 {len(validation_result.success_records)} 条记录")
            
            return ProcessingResult(
                success=True,
                processed_count=len(validation_result.success_records),
                failed_count=0,
                success_records=validation_result.success_records,
                failed_results=[],
                error_message=None
            )
        
        except Exception as e:
            error_msg = f"处理过程中发生异常: {str(e)}"
            log_error(error_msg, e)
            return ProcessingResult(
                success=False,
                processed_count=0,
                failed_count=0,
                success_records=[],
                failed_results=[],
                error_message=error_msg
            )
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态信息
        
        Returns:
            系统状态字典
        """
        try:
            # 获取规则摘要
            rule_summary = self.rule_validator.get_rule_summary()
            
            # 获取统计数据
            customer_stats, yearly_stats = self.statistics_manager.load_current_statistics()
            
            # 获取主明细表数据
            main_detail_df = self.data_processor.read_excel_file(
                os.path.join(
                    self.data_processor.data_paths.get('raw_data_dir', 'Data/Raw'),
                    self.data_processor.data_files.get('application_main_detail', '金豆发放申请主明细表.xlsx')
                )
            )
            
            status = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "rules": rule_summary,
                "statistics": {
                    "customer_count": len(customer_stats),
                    "yearly_records": len(yearly_stats),
                    "main_detail_records": len(main_detail_df) if not main_detail_df.empty else 0
                },
                "data_files": {
                    "raw_data_dir": self.data_processor.data_paths.get('raw_data_dir', 'Data/Raw'),
                    "backup_dir": self.statistics_manager.backup_dir
                }
            }
            
            return status
        
        except Exception as e:
            log_error("获取系统状态失败", e)
            return {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "error": str(e)
            }

def print_system_banner():
    """打印系统横幅"""
    print_banner("金豆发放监控与分析系统 v1.0.0", 80)
    print_timestamp("启动时间: ")

def main():
    """主函数"""
    print_system_banner()

    try:
        # 初始化处理器
        print_section("系统初始化")
        print_module_action("SYSTEM", "正在初始化处理器")
        processor = GoldBeanProcessor()

        # 显示系统状态
        print_section("系统状态检查")
        status = processor.get_system_status()

        status_info = {
            "系统时间": status['timestamp'],
            "生效规则数": status.get('rules', {}).get('active_rules', 0),
            "客户统计记录": status.get('statistics', {}).get('customer_count', 0),
            "主明细记录": status.get('statistics', {}).get('main_detail_records', 0)
        }
        print_summary("系统状态", status_info)

        # 处理金豆发放申请
        print_section("数据处理")
        result = processor.process_application_details()

        # 显示处理结果
        print_section("处理结果")

        result_info = {
            "处理状态": "成功" if result.success else "失败",
            "处理记录数": result.processed_count,
            "失败记录数": result.failed_count
        }
        print_summary("处理统计", result_info)

        if result.error_message:
            print_module_action("SYSTEM", "处理过程中发现问题", "WARNING", result.error_message)

        if result.success:
            print_result(True, f"处理完成！成功处理 {result.processed_count} 条记录")
        else:
            print_result(False, f"处理失败！{result.error_message}")

        print_section("后续操作")
        print_module_action("INFO", "详细日志请查看 logs 目录", "INFO")

    except Exception as e:
        print_result(False, f"系统运行异常: {e}")
        log_error("系统运行异常", e)
        return 1

    return 0

if __name__ == "__main__":
    exit_code = main()
    print_timestamp("\n程序结束时间: ")
    sys.exit(exit_code)
