#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志符号模块
为不同的处理阶段提供专用的符号标识，提升日志可读性
"""

class LogSymbols:
    """日志符号常量类"""
    
    # 文件操作相关
    FILE_SEARCH = "[📁]"          # 文件查找
    FILE_READ = "[📖]"            # 文件读取
    FILE_WRITE = "[💾]"           # 文件写入/数据备份
    
    # 数据处理相关
    DATA_CLEAN = "[🧹]"           # 数据清洗
    BATCH_PROCESS = "[⚡]"        # 批量处理
    CONFLICT_CHECK = "[🔍]"       # 冲突检测
    DUPLICATE_REMOVE = "[🔄]"     # 去重处理
    DETAIL_PROCESS = "[📋]"       # 申请明细处理
    
    # 校验相关
    SINGLE_LIMIT = "[💰]"         # 单次发放限额校验
    YEARLY_LIMIT = "[📅]"         # 年累计发放额度校验
    CUSTOMER_LIMIT = "[👤]"       # 户累计发放额度校验
    RULE_VALIDATE = "[✅]"        # 规则校验
    
    # 统计相关
    CUSTOMER_STATS = "[📊]"       # 客户统计更新
    YEARLY_STATS = "[📈]"         # 年度统计更新
    STATS_AGGREGATE = "[🔢]"      # 统计汇总
    
    # 系统操作相关
    SYSTEM_START = "[🚀]"         # 系统启动
    SYSTEM_END = "[🏁]"           # 系统结束
    ERROR_HANDLE = "[❌]"         # 错误处理
    WARNING = "[⚠️]"              # 警告信息
    SUCCESS = "[✅]"              # 成功信息
    
    # 业务流程相关
    WORKFLOW_START = "[🔄]"       # 工作流开始
    WORKFLOW_END = "[✔️]"         # 工作流结束
    STEP_PROCESS = "[📝]"         # 步骤处理
    
    # 数据问题相关
    PROBLEM_FOUND = "[🚨]"        # 发现问题
    PROBLEM_DETAIL = "[📄]"       # 问题详情
    PROBLEM_SUMMARY = "[📋]"      # 问题汇总

def format_log_message(symbol: str, stage: str, message: str, details: str = None) -> str:
    """
    格式化日志消息
    
    Args:
        symbol: 日志符号
        stage: 处理阶段
        message: 主要消息
        details: 详细信息（可选）
    
    Returns:
        格式化后的日志消息
    """
    if details:
        return f"{symbol} {stage} - {message} | {details}"
    else:
        return f"{symbol} {stage} - {message}"

def log_file_operation(logger, operation: str, file_path: str, result: str = None):
    """记录文件操作日志"""
    if operation == "search":
        symbol = LogSymbols.FILE_SEARCH
    elif operation == "read":
        symbol = LogSymbols.FILE_READ
    elif operation == "write":
        symbol = LogSymbols.FILE_WRITE
    else:
        symbol = LogSymbols.FILE_READ
    
    message = f"{operation.upper()} {file_path}"
    if result:
        message += f" - {result}"
    
    logger.info(format_log_message(symbol, "文件操作", message))

def log_data_processing(logger, stage: str, message: str, count: int = None):
    """记录数据处理日志"""
    if "清洗" in stage:
        symbol = LogSymbols.DATA_CLEAN
    elif "批量" in stage:
        symbol = LogSymbols.BATCH_PROCESS
    elif "冲突" in stage:
        symbol = LogSymbols.CONFLICT_CHECK
    elif "去重" in stage:
        symbol = LogSymbols.DUPLICATE_REMOVE
    elif "明细" in stage:
        symbol = LogSymbols.DETAIL_PROCESS
    else:
        symbol = LogSymbols.STEP_PROCESS
    
    details = f"处理数量: {count}" if count is not None else None
    logger.info(format_log_message(symbol, stage, message, details))

def log_validation(logger, validation_type: str, message: str, result: str = None):
    """记录校验日志"""
    if "单次" in validation_type:
        symbol = LogSymbols.SINGLE_LIMIT
    elif "年累计" in validation_type or "年度" in validation_type:
        symbol = LogSymbols.YEARLY_LIMIT
    elif "户累计" in validation_type or "客户" in validation_type:
        symbol = LogSymbols.CUSTOMER_LIMIT
    else:
        symbol = LogSymbols.RULE_VALIDATE
    
    logger.info(format_log_message(symbol, validation_type, message, result))

def log_statistics(logger, stats_type: str, message: str, details: str = None):
    """记录统计日志"""
    if "客户统计" in stats_type:
        symbol = LogSymbols.CUSTOMER_STATS
    elif "年度统计" in stats_type or "年统计" in stats_type:
        symbol = LogSymbols.YEARLY_STATS
    elif "汇总" in stats_type:
        symbol = LogSymbols.STATS_AGGREGATE
    else:
        symbol = LogSymbols.CUSTOMER_STATS
    
    logger.info(format_log_message(symbol, stats_type, message, details))

def log_problem(logger, problem_type: str, message: str, details: str = None):
    """记录问题日志"""
    if "发现" in problem_type:
        symbol = LogSymbols.PROBLEM_FOUND
    elif "详情" in problem_type:
        symbol = LogSymbols.PROBLEM_DETAIL
    elif "汇总" in problem_type:
        symbol = LogSymbols.PROBLEM_SUMMARY
    else:
        symbol = LogSymbols.PROBLEM_FOUND
    
    logger.error(format_log_message(symbol, problem_type, message, details))

def log_workflow(logger, workflow_stage: str, message: str, status: str = None):
    """记录工作流日志"""
    if "开始" in workflow_stage:
        symbol = LogSymbols.WORKFLOW_START
    elif "结束" in workflow_stage or "完成" in workflow_stage:
        symbol = LogSymbols.WORKFLOW_END
    else:
        symbol = LogSymbols.STEP_PROCESS
    
    if status == "SUCCESS":
        symbol = LogSymbols.SUCCESS
    elif status == "ERROR":
        symbol = LogSymbols.ERROR_HANDLE
    elif status == "WARNING":
        symbol = LogSymbols.WARNING
    
    logger.info(format_log_message(symbol, workflow_stage, message))
