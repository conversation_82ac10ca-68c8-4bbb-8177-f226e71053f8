#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金豆发放监控与分析系统优化测试脚本
验证文件读取、机构历史记录、冲突检测性能等优化功能
"""

import pandas as pd
import os
import sys
import time
import tempfile
from typing import List, Dict, Any

# 添加src目录到路径
sys.path.append('src')

from src.models import ApplicationRecord, CustomerStatistics, YearlyStatistics
from src.data_processor import DataProcessor
from src.logger import get_logger

def test_file_filtering():
    """测试Excel临时文件过滤功能"""
    print("=" * 60)
    print("测试 Excel临时文件过滤功能")
    print("=" * 60)
    
    processor = DataProcessor()
    
    # 创建临时目录和文件进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建正常的Excel文件
        normal_file = os.path.join(temp_dir, "金豆发放申请明细表-测试.xlsx")
        temp_file = os.path.join(temp_dir, "~$金豆发放申请明细表-测试.xlsx")
        
        # 创建测试文件
        test_df = pd.DataFrame({'测试': [1, 2, 3]})
        test_df.to_excel(normal_file, index=False)
        
        # 创建临时文件（模拟Excel打开时的临时文件）
        with open(temp_file, 'w') as f:
            f.write("临时文件内容")
        
        # 修改处理器的数据路径指向临时目录
        original_path = processor.data_paths.get('raw_data_dir', 'Data/Raw')
        processor.data_paths['raw_data_dir'] = temp_dir
        
        try:
            # 测试文件查找
            files = processor.find_application_detail_files()
            
            print(f"✓ 找到文件数量: {len(files)}")
            print(f"✓ 正常文件: {[os.path.basename(f) for f in files]}")
            
            # 验证临时文件被过滤
            temp_files_found = [f for f in files if os.path.basename(f).startswith('~$')]
            if len(temp_files_found) == 0:
                print("✅ Excel临时文件过滤功能正常")
            else:
                print("❌ Excel临时文件过滤功能异常")
                
        finally:
            # 恢复原始路径
            processor.data_paths['raw_data_dir'] = original_path
    
    print("Excel临时文件过滤测试完成\n")

def test_organization_history():
    """测试机构历史记录累积功能"""
    print("=" * 60)
    print("测试机构历史记录累积功能")
    print("=" * 60)
    
    # 测试CustomerStatistics
    stat = CustomerStatistics(
        customer_name="张三",
        customer_phone="13800138000",
        customer_enterprise="测试企业",
        organization="机构A",
        total_amount=500,
        total_count=3
    )
    
    print(f"✓ 初始机构: {stat.organization}")
    
    # 添加新机构
    stat.append_organization_history("机构B")
    print(f"✓ 添加机构B后: {stat.organization}")
    
    # 添加第三个机构
    stat.append_organization_history("机构C")
    print(f"✓ 添加机构C后: {stat.organization}")
    
    # 尝试添加重复机构
    stat.append_organization_history("机构A")
    print(f"✓ 重复添加机构A后: {stat.organization}")
    
    # 验证分号分隔
    expected_orgs = ["机构A", "机构B", "机构C"]
    actual_orgs = [org.strip() for org in stat.organization.split('；')]
    
    if set(expected_orgs) == set(actual_orgs) and len(actual_orgs) == 3:
        print("✅ 机构历史记录累积功能正常")
    else:
        print("❌ 机构历史记录累积功能异常")
        print(f"期望: {expected_orgs}")
        print(f"实际: {actual_orgs}")
    
    print("机构历史记录累积测试完成\n")

def test_conflict_detection_performance():
    """测试冲突检测性能优化"""
    print("=" * 60)
    print("测试冲突检测性能优化")
    print("=" * 60)
    
    processor = DataProcessor()
    
    # 创建测试数据
    def create_test_data(size: int, prefix: str) -> pd.DataFrame:
        data = []
        for i in range(size):
            data.append({
                '客户名称': f'{prefix}客户{i}',
                '客户手机号': f'138{i:08d}',
                '客户归属企业': f'{prefix}企业{i % 10}',
                '等值金豆发放数量': 100 + i,
                '申请发放机构': f'{prefix}机构{i % 5}',
                '申请发放日期': '2025-01-15'
            })
        return pd.DataFrame(data)
    
    # 测试不同数据量的性能
    test_sizes = [100, 500, 1000]
    
    for size in test_sizes:
        print(f"测试数据量: {size} 条记录")
        
        new_df = create_test_data(size, "新")
        main_df = create_test_data(size, "主")
        
        start_time = time.time()
        has_conflict, conflicts = processor.detect_data_conflicts(new_df, main_df)
        end_time = time.time()
        
        processing_time = end_time - start_time
        speed = (size * 2) / processing_time if processing_time > 0 else 0
        
        print(f"  - 处理时间: {processing_time:.3f}秒")
        print(f"  - 处理速度: {speed:.0f} 条/秒")
        print(f"  - 冲突检测结果: {has_conflict}")
        print(f"  - 冲突数量: {len(conflicts)}")
    
    # 测试有冲突的情况
    print("\n测试冲突检测准确性:")
    conflict_new_df = pd.DataFrame([
        {
            '客户名称': '张三',
            '客户手机号': '13800138000',
            '客户归属企业': '企业A',
            '等值金豆发放数量': 100,
            '申请发放机构': '机构A',
            '申请发放日期': '2025-01-15'
        }
    ])
    
    conflict_main_df = pd.DataFrame([
        {
            '客户名称': '李四',  # 不同的客户名称
            '客户手机号': '13800138000',  # 相同的手机号
            '客户归属企业': '企业A',  # 相同的企业
            '等值金豆发放数量': 200,
            '申请发放机构': '机构B',
            '申请发放日期': '2025-01-16'
        }
    ])
    
    has_conflict, conflicts = processor.detect_data_conflicts(conflict_new_df, conflict_main_df)
    
    if has_conflict and len(conflicts) > 0:
        print("✅ 冲突检测准确性正常")
        print(f"  检测到冲突: {conflicts[0]}")
    else:
        print("❌ 冲突检测准确性异常")
    
    print("冲突检测性能优化测试完成\n")

def test_main_program():
    """测试主程序运行"""
    print("=" * 60)
    print("测试主程序运行")
    print("=" * 60)
    
    try:
        # 导入主程序模块
        import main
        
        print("✓ 主程序模块导入成功")
        
        # 检查关键函数是否存在
        if hasattr(main, 'main'):
            print("✓ main函数存在")
        else:
            print("❌ main函数不存在")
        
        print("✅ 主程序基本结构正常")
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
    
    print("主程序测试完成\n")

def run_all_tests():
    """运行所有测试"""
    print("开始系统优化测试")
    print("=" * 80)
    
    try:
        test_file_filtering()
        test_organization_history()
        test_conflict_detection_performance()
        test_main_program()
        
        print("=" * 80)
        print("✅ 所有优化测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
