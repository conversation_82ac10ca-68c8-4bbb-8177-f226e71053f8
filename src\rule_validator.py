#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则校验引擎模块
实现单次发放、年累计、户累计三种规则校验逻辑，支持动态规则配置和原子性处理
"""

import pandas as pd
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from src.utils import load_config, extract_year_from_date, retry_operation
from src.logger import get_logger, log_rule_validation, log_error
from src.models import (
    ApplicationRecord, 
    ValidationRule, 
    ValidationResult, 
    ProcessingResult,
    CustomerStatistics,
    YearlyStatistics,
    DataFrameConverter
)

class RuleValidator:
    """规则校验引擎"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化规则校验引擎
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        self.logger = get_logger("rule_validator")
        
        # 获取配置参数
        self.data_paths = self.config.get('data_paths', {})
        self.data_files = self.config.get('data_files', {})
        self.rule_config = self.config.get('rule_validation', {})
        
        # 加载规则配置
        self.rules = self._load_rules()
        
        # 加载统计数据
        self.customer_stats = self._load_customer_statistics()
        self.yearly_stats = self._load_yearly_statistics()
    
    def _load_rules(self) -> List[ValidationRule]:
        """
        加载规则配置
        
        Returns:
            规则列表
        """
        try:
            rules_file = os.path.join(
                self.data_paths.get('raw_data_dir', 'Data/Raw'),
                self.data_files.get('rules_config', '金豆发放规则配置表.xlsx')
            )
            
            if not os.path.exists(rules_file):
                self.logger.warning(f"规则配置文件不存在: {rules_file}")
                return self._get_default_rules()
            
            df = retry_operation(
                func=pd.read_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[rules_file]
            )
            
            rules = DataFrameConverter.to_validation_rules(df)
            
            # 只返回生效的规则
            active_rules = [rule for rule in rules if rule.is_active]
            
            self.logger.info(f"成功加载 {len(active_rules)} 条生效规则")
            return active_rules
        
        except Exception as e:
            log_error("加载规则配置失败", e)
            return self._get_default_rules()
    
    def _get_default_rules(self) -> List[ValidationRule]:
        """
        获取默认规则配置
        
        Returns:
            默认规则列表
        """
        default_limits = self.rule_config.get('default_limits', {})
        
        return [
            ValidationRule(
                rule_id=1,
                rule_code="SINGLE_LIMIT",
                rule_name="单次发放限额",
                limit_value=default_limits.get('single_limit', 1000),
                period_unit="次",
                is_active=True,
                description="单户单次充值不超过限额",
                last_update=datetime.now().strftime("%Y%m%d")
            ),
            ValidationRule(
                rule_id=2,
                rule_code="YEAR_LIMIT",
                rule_name="年累计发放额度",
                limit_value=default_limits.get('yearly_limit', 10000),
                period_unit="年累计",
                is_active=True,
                description="年累计不超过限额",
                last_update=datetime.now().strftime("%Y%m%d")
            ),
            ValidationRule(
                rule_id=3,
                rule_code="CUSTOMER_TOTAL_LIMIT",
                rule_name="户累计发放额度",
                limit_value=default_limits.get('lifetime_limit', 30000),
                period_unit="户累计",
                is_active=True,
                description="户累计不超过限额",
                last_update=datetime.now().strftime("%Y%m%d")
            )
        ]
    
    def _load_customer_statistics(self) -> Dict[str, CustomerStatistics]:
        """
        加载客户统计数据
        
        Returns:
            客户统计数据字典，键为客户主键
        """
        try:
            stats_file = os.path.join(
                self.data_paths.get('raw_data_dir', 'Data/Raw'),
                self.data_files.get('statistics_table', '金豆发放统计表.xlsx')
            )
            
            if not os.path.exists(stats_file):
                self.logger.info("客户统计表不存在，返回空统计")
                return {}
            
            df = retry_operation(
                func=pd.read_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[stats_file]
            )
            
            if df.empty:
                self.logger.info("客户统计表为空")
                return {}
            
            stats_list = DataFrameConverter.to_customer_statistics(df)
            
            # 转换为字典，键为客户主键
            stats_dict = {}
            for stat in stats_list:
                key = stat.get_customer_key()
                stats_dict[key] = stat
            
            self.logger.info(f"成功加载 {len(stats_dict)} 条客户统计记录")
            return stats_dict
        
        except Exception as e:
            log_error("加载客户统计数据失败", e)
            return {}
    
    def _load_yearly_statistics(self) -> Dict[str, YearlyStatistics]:
        """
        加载年度统计数据
        
        Returns:
            年度统计数据字典，键为年度主键
        """
        try:
            yearly_file = os.path.join(
                self.data_paths.get('raw_data_dir', 'Data/Raw'),
                self.data_files.get('yearly_statistics', '金豆发放年统计表.xlsx')
            )
            
            if not os.path.exists(yearly_file):
                self.logger.info("年度统计表不存在，返回空统计")
                return {}
            
            df = retry_operation(
                func=pd.read_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[yearly_file]
            )
            
            if df.empty:
                self.logger.info("年度统计表为空")
                return {}
            
            stats_list = DataFrameConverter.to_yearly_statistics(df)
            
            # 转换为字典，键为年度主键
            stats_dict = {}
            for stat in stats_list:
                key = stat.get_yearly_key()
                stats_dict[key] = stat
            
            self.logger.info(f"成功加载 {len(stats_dict)} 条年度统计记录")
            return stats_dict
        
        except Exception as e:
            log_error("加载年度统计数据失败", e)
            return {}
    
    def _get_rule_by_code(self, rule_code: str) -> Optional[ValidationRule]:
        """
        根据规则代码获取规则
        
        Args:
            rule_code: 规则代码
            
        Returns:
            规则对象或None
        """
        for rule in self.rules:
            if rule.rule_code == rule_code:
                return rule
        return None
    
    def _validate_single_limit(self, record: ApplicationRecord) -> Tuple[bool, Optional[ValidationRule]]:
        """
        校验单次发放限额
        
        Args:
            record: 申请记录
            
        Returns:
            (是否通过, 违反的规则)
        """
        rule = self._get_rule_by_code("SINGLE_LIMIT")
        if not rule:
            return True, None
        
        is_valid = record.amount <= rule.limit_value
        
        log_rule_validation(
            rule.rule_name,
            "PASS" if is_valid else "FAIL",
            f"客户 {record.customer_name} 申请发放 {record.amount}，限额 {rule.limit_value}"
        )
        
        return is_valid, None if is_valid else rule
    
    def _validate_yearly_limit(self, record: ApplicationRecord) -> Tuple[bool, Optional[ValidationRule]]:
        """
        校验年累计发放限额
        
        Args:
            record: 申请记录
            
        Returns:
            (是否通过, 违反的规则)
        """
        rule = self._get_rule_by_code("YEAR_LIMIT")
        if not rule:
            return True, None
        
        # 获取申请年份
        year = extract_year_from_date(record.application_date)
        
        # 构建年度主键
        yearly_key = f"{record.customer_name}_{record.customer_phone}_{year}"
        
        # 获取年度累计
        yearly_stat = self.yearly_stats.get(yearly_key)
        current_yearly_amount = yearly_stat.yearly_amount if yearly_stat else 0
        
        # 计算新的年度累计
        new_yearly_amount = current_yearly_amount + record.amount
        
        is_valid = new_yearly_amount <= rule.limit_value
        
        log_rule_validation(
            rule.rule_name,
            "PASS" if is_valid else "FAIL",
            f"客户 {record.customer_name} {year}年累计 {current_yearly_amount} + 本次 {record.amount} = {new_yearly_amount}，限额 {rule.limit_value}"
        )
        
        return is_valid, None if is_valid else rule
    
    def _validate_lifetime_limit(self, record: ApplicationRecord) -> Tuple[bool, Optional[ValidationRule]]:
        """
        校验户累计发放限额
        
        Args:
            record: 申请记录
            
        Returns:
            (是否通过, 违反的规则)
        """
        rule = self._get_rule_by_code("CUSTOMER_TOTAL_LIMIT")
        if not rule:
            return True, None
        
        # 构建客户主键
        customer_key = f"{record.customer_name}_{record.customer_phone}"
        
        # 获取户累计
        customer_stat = self.customer_stats.get(customer_key)
        current_total_amount = customer_stat.total_amount if customer_stat else 0
        
        # 计算新的户累计
        new_total_amount = current_total_amount + record.amount
        
        is_valid = new_total_amount <= rule.limit_value
        
        log_rule_validation(
            rule.rule_name,
            "PASS" if is_valid else "FAIL",
            f"客户 {record.customer_name} 户累计 {current_total_amount} + 本次 {record.amount} = {new_total_amount}，限额 {rule.limit_value}"
        )
        
        return is_valid, None if is_valid else rule

    def validate_record(self, record: ApplicationRecord) -> ValidationResult:
        """
        校验单条记录

        Args:
            record: 申请记录

        Returns:
            校验结果
        """
        violated_rules = []
        error_messages = []

        try:
            # 1. 校验单次发放限额
            is_single_valid, single_rule = self._validate_single_limit(record)
            if not is_single_valid and single_rule:
                violated_rules.append(single_rule)
                error_messages.append(f"单次发放数量 {record.amount} 超过限额 {single_rule.limit_value}")

            # 2. 校验年累计发放限额
            is_yearly_valid, yearly_rule = self._validate_yearly_limit(record)
            if not is_yearly_valid and yearly_rule:
                violated_rules.append(yearly_rule)
                error_messages.append(f"年累计发放超过限额 {yearly_rule.limit_value}")

            # 3. 校验户累计发放限额
            is_lifetime_valid, lifetime_rule = self._validate_lifetime_limit(record)
            if not is_lifetime_valid and lifetime_rule:
                violated_rules.append(lifetime_rule)
                error_messages.append(f"户累计发放超过限额 {lifetime_rule.limit_value}")

            is_valid = len(violated_rules) == 0

            return ValidationResult(
                record=record,
                is_valid=is_valid,
                violated_rules=violated_rules,
                error_messages=error_messages
            )

        except Exception as e:
            log_error(f"校验记录失败: {record.get_business_key()}", e)
            return ValidationResult(
                record=record,
                is_valid=False,
                violated_rules=[],
                error_messages=[f"校验过程中发生错误: {str(e)}"]
            )

    def validate_batch(self, records: List[ApplicationRecord]) -> ProcessingResult:
        """
        批量校验记录（原子性处理）

        Args:
            records: 申请记录列表

        Returns:
            处理结果
        """
        try:
            if not records:
                return ProcessingResult(
                    success=True,
                    processed_count=0,
                    failed_count=0,
                    success_records=[],
                    failed_results=[],
                    error_message="没有记录需要校验"
                )

            success_records = []
            failed_results = []

            # 逐条校验记录
            for record in records:
                validation_result = self.validate_record(record)

                if validation_result.is_valid:
                    success_records.append(record)
                else:
                    failed_results.append(validation_result)

            # 原子性处理：如果有任何记录失败，整个批次都失败
            if failed_results:
                error_details = []
                for failed_result in failed_results:
                    record = failed_result.record
                    error_details.append(
                        f"客户 {record.customer_name}（{record.customer_phone}）: {', '.join(failed_result.error_messages)}"
                    )

                error_message = f"批处理失败，{len(failed_results)} 条记录校验失败：\n" + "\n".join(error_details)

                return ProcessingResult(
                    success=False,
                    processed_count=0,
                    failed_count=len(records),
                    success_records=[],
                    failed_results=failed_results,
                    error_message=error_message
                )

            # 所有记录都通过校验
            return ProcessingResult(
                success=True,
                processed_count=len(success_records),
                failed_count=0,
                success_records=success_records,
                failed_results=[],
                error_message=None
            )

        except Exception as e:
            log_error("批量校验失败", e)
            return ProcessingResult(
                success=False,
                processed_count=0,
                failed_count=len(records),
                success_records=[],
                failed_results=[],
                error_message=f"批量校验过程中发生错误: {str(e)}"
            )

    def get_rule_summary(self) -> Dict[str, Any]:
        """
        获取规则摘要信息

        Returns:
            规则摘要字典
        """
        summary = {
            "total_rules": len(self.rules),
            "active_rules": len([r for r in self.rules if r.is_active]),
            "rules_detail": []
        }

        for rule in self.rules:
            summary["rules_detail"].append({
                "rule_code": rule.rule_code,
                "rule_name": rule.rule_name,
                "limit_value": rule.limit_value,
                "period_unit": rule.period_unit,
                "is_active": rule.is_active,
                "description": rule.description
            })

        return summary

# 测试函数
def test_rule_validator():
    """测试规则校验引擎"""
    print("=== 测试规则校验引擎 ===")

    validator = RuleValidator()

    # 测试规则加载
    print(f"\n1. 规则加载测试")
    rule_summary = validator.get_rule_summary()
    print(f"总规则数: {rule_summary['total_rules']}")
    print(f"生效规则数: {rule_summary['active_rules']}")

    for rule_detail in rule_summary['rules_detail']:
        print(f"  - {rule_detail['rule_name']}: {rule_detail['limit_value']} ({rule_detail['period_unit']})")

    # 创建测试记录
    test_records = [
        ApplicationRecord(
            record_id=1,
            customer_name="测试客户1",
            customer_phone="13800138001",
            amount=500,  # 正常金额
            organization="测试机构",
            application_date="2025-01-15"
        ),
        ApplicationRecord(
            record_id=2,
            customer_name="测试客户2",
            customer_phone="13800138002",
            amount=1500,  # 超过单次限额
            organization="测试机构",
            application_date="2025-01-15"
        )
    ]

    # 测试单条记录校验
    print(f"\n2. 单条记录校验测试")
    for i, record in enumerate(test_records):
        result = validator.validate_record(record)
        print(f"记录 {i+1}: {result.get_violation_summary()}")
        if not result.is_valid:
            for error in result.error_messages:
                print(f"  错误: {error}")

    # 测试批量校验
    print(f"\n3. 批量校验测试")
    batch_result = validator.validate_batch(test_records)
    print(f"批量校验结果: {batch_result.get_summary()}")
    if not batch_result.success:
        print(f"错误信息: {batch_result.error_message}")

    print("\n=== 规则校验引擎测试完成 ===")

if __name__ == "__main__":
    test_rule_validator()
