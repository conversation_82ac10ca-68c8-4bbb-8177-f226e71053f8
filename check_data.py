#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def check_data_files():
    """检查数据文件"""
    try:
        # 检查申请明细表
        if os.path.exists('Data/Raw/金豆发放申请明细表.xlsx'):
            df_detail = pd.read_excel('Data/Raw/金豆发放申请明细表.xlsx')
            print(f"申请明细表记录数: {len(df_detail)}")
            print(f"申请明细表列名: {df_detail.columns.tolist()}")
            if len(df_detail) > 0:
                print("申请明细表前3行:")
                print(df_detail.head(3))
        else:
            print("申请明细表文件不存在")
        
        print("\n" + "="*50 + "\n")
        
        # 检查主明细表
        if os.path.exists('Data/Raw/金豆发放申请主明细表.xlsx'):
            df_main = pd.read_excel('Data/Raw/金豆发放申请主明细表.xlsx')
            print(f"主明细表记录数: {len(df_main)}")
            print(f"主明细表列名: {df_main.columns.tolist()}")
            if len(df_main) > 0:
                print("主明细表前3行:")
                print(df_main.head(3))
        else:
            print("主明细表文件不存在")
            
    except Exception as e:
        print(f"检查数据文件时出错: {e}")

if __name__ == "__main__":
    check_data_files()
