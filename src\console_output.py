#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制台输出格式化工具
提供统一的控制台输出格式，包括颜色、分隔符和状态标识
"""

import sys
from datetime import datetime
from typing import Optional

class Colors:
    """控制台颜色定义"""
    # 基础颜色
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # 亮色
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'
    
    # 样式
    BOLD = '\033[1m'
    DIM = '\033[2m'
    UNDERLINE = '\033[4m'
    
    # 重置
    RESET = '\033[0m'
    END = '\033[0m'

class ConsoleFormatter:
    """控制台格式化器"""
    
    def __init__(self, enable_colors: bool = True):
        """
        初始化控制台格式化器
        
        Args:
            enable_colors: 是否启用颜色输出
        """
        self.enable_colors = enable_colors and sys.stdout.isatty()
    
    def _colorize(self, text: str, color: str) -> str:
        """
        为文本添加颜色
        
        Args:
            text: 要着色的文本
            color: 颜色代码
            
        Returns:
            着色后的文本
        """
        if self.enable_colors:
            return f"{color}{text}{Colors.RESET}"
        return text
    
    def print_banner(self, title: str, width: int = 80, char: str = "="):
        """
        打印横幅标题
        
        Args:
            title: 标题文本
            width: 横幅宽度
            char: 分隔符字符
        """
        border = char * width
        title_line = f"{char * 2} {title} {char * (width - len(title) - 5)}"
        
        print(self._colorize(border, Colors.BRIGHT_CYAN))
        print(self._colorize(title_line, Colors.BRIGHT_CYAN + Colors.BOLD))
        print(self._colorize(border, Colors.BRIGHT_CYAN))
    
    def print_section(self, section_name: str, width: int = 60):
        """
        打印章节标题
        
        Args:
            section_name: 章节名称
            width: 分隔线宽度
        """
        separator = "-" * width
        section_line = f">>> {section_name} <<<"
        
        print()
        print(self._colorize(separator, Colors.BRIGHT_BLUE))
        print(self._colorize(section_line, Colors.BRIGHT_BLUE + Colors.BOLD))
        print(self._colorize(separator, Colors.BRIGHT_BLUE))
    
    def print_step(self, step_num: int, description: str, total_steps: Optional[int] = None):
        """
        打印步骤信息
        
        Args:
            step_num: 步骤编号
            description: 步骤描述
            total_steps: 总步骤数
        """
        if total_steps:
            step_text = f"步骤 {step_num}/{total_steps}"
        else:
            step_text = f"步骤 {step_num}"
        
        print(f"\n{self._colorize(step_text, Colors.BRIGHT_YELLOW + Colors.BOLD)}: {description}")
    
    def print_module_action(self, module_name: str, action: str, status: str = "", details: str = ""):
        """
        打印模块操作信息
        
        Args:
            module_name: 模块名称
            action: 操作描述
            status: 状态（SUCCESS, ERROR, WARNING, INFO）
            details: 详细信息
        """
        # 状态颜色映射
        status_colors = {
            "SUCCESS": Colors.BRIGHT_GREEN,
            "ERROR": Colors.BRIGHT_RED,
            "WARNING": Colors.BRIGHT_YELLOW,
            "INFO": Colors.BRIGHT_BLUE,
            "": Colors.WHITE
        }
        
        # 状态符号映射
        status_symbols = {
            "SUCCESS": "✓",
            "ERROR": "✗",
            "WARNING": "⚠",
            "INFO": "ℹ",
            "": "•"
        }
        
        color = status_colors.get(status, Colors.WHITE)
        symbol = status_symbols.get(status, "•")
        
        # 格式化输出
        module_part = self._colorize(f"[{module_name}]", Colors.BRIGHT_MAGENTA + Colors.BOLD)
        action_part = action
        
        if status:
            status_part = self._colorize(f"{symbol} {status}", color + Colors.BOLD)
            print(f"{module_part} {action_part} - {status_part}")
        else:
            print(f"{module_part} {action_part}")
        
        if details:
            detail_lines = details.split('\n')
            for line in detail_lines:
                if line.strip():
                    print(f"    {self._colorize('│', Colors.DIM)} {line}")
    
    def print_result(self, success: bool, message: str, details: Optional[str] = None):
        """
        打印结果信息
        
        Args:
            success: 是否成功
            message: 结果消息
            details: 详细信息
        """
        if success:
            symbol = self._colorize("✓", Colors.BRIGHT_GREEN + Colors.BOLD)
            status = self._colorize("成功", Colors.BRIGHT_GREEN + Colors.BOLD)
        else:
            symbol = self._colorize("✗", Colors.BRIGHT_RED + Colors.BOLD)
            status = self._colorize("失败", Colors.BRIGHT_RED + Colors.BOLD)
        
        print(f"\n{symbol} {status}: {message}")
        
        if details:
            detail_lines = details.split('\n')
            for line in detail_lines:
                if line.strip():
                    print(f"    {line}")
    
    def print_summary(self, title: str, items: dict):
        """
        打印摘要信息
        
        Args:
            title: 摘要标题
            items: 摘要项目字典
        """
        print(f"\n{self._colorize(title, Colors.BRIGHT_WHITE + Colors.BOLD + Colors.UNDERLINE)}")
        
        for key, value in items.items():
            key_part = self._colorize(f"{key}:", Colors.BRIGHT_YELLOW)
            print(f"  {key_part} {value}")
    
    def print_timestamp(self, prefix: str = ""):
        """
        打印时间戳

        Args:
            prefix: 前缀文本
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        timestamp_text = f"{prefix}{timestamp}" if prefix else timestamp
        print(self._colorize(timestamp_text, Colors.BRIGHT_BLACK))

    def print_conflict_detection_result(self, has_conflict: bool, conflicts: list):
        """
        打印数据冲突检测结果（优化格式）

        Args:
            has_conflict: 是否有冲突
            conflicts: 冲突详情列表
        """
        separator = "=" * 60
        print(f"\n{self._colorize(separator, Colors.BRIGHT_CYAN)}")
        print(f"{self._colorize('数据冲突检测结果', Colors.BRIGHT_CYAN + Colors.BOLD)}")
        print(f"{self._colorize(separator, Colors.BRIGHT_CYAN)}")

        if not has_conflict:
            print(f"{self._colorize('✓ 未发现数据冲突', Colors.BRIGHT_GREEN + Colors.BOLD)}")
        else:
            print(f"{self._colorize(f'✗ 发现 {len(conflicts)} 个冲突记录', Colors.BRIGHT_RED + Colors.BOLD)}")
            print()

            for i, conflict in enumerate(conflicts, 1):
                if isinstance(conflict, str) and '|' in conflict:
                    # 解析格式化的冲突信息
                    parts = conflict.split('|')
                    if len(parts) >= 5:
                        conflict_type = parts[0]
                        detail1 = parts[1]
                        detail2 = parts[2]
                        detail3 = parts[3]
                        detail4 = parts[4]

                        print(f"{self._colorize(f'冲突记录 {i}:', Colors.BRIGHT_YELLOW + Colors.BOLD)}")
                        print(f"  - 冲突类型: {self._colorize(conflict_type, Colors.BRIGHT_RED)}")
                        print(f"  - {detail1}")
                        print(f"  - {detail2}")
                        print(f"  - {detail3}")
                        print(f"  - {detail4}")
                        print()
                else:
                    # 兼容旧格式
                    print(f"{self._colorize(f'冲突记录 {i}:', Colors.BRIGHT_YELLOW + Colors.BOLD)}")
                    print(f"  {conflict}")
                    print()

        print(f"{self._colorize(separator, Colors.BRIGHT_CYAN)}")

    def print_limit_validation_result(self, success: bool, failed_results: list):
        """
        打印金豆限额校验结果（优化格式）

        Args:
            success: 是否校验成功
            failed_results: 失败的校验结果列表
        """
        separator = "=" * 60
        print(f"\n{self._colorize(separator, Colors.BRIGHT_MAGENTA)}")
        print(f"{self._colorize('金豆限额校验结果', Colors.BRIGHT_MAGENTA + Colors.BOLD)}")
        print(f"{self._colorize(separator, Colors.BRIGHT_MAGENTA)}")

        if success:
            print(f"{self._colorize('✓ 所有记录均通过限额校验', Colors.BRIGHT_GREEN + Colors.BOLD)}")
        else:
            print(f"{self._colorize(f'✗ 发现 {len(failed_results)} 个客户超限', Colors.BRIGHT_RED + Colors.BOLD)}")
            print()

            for i, failed_result in enumerate(failed_results, 1):
                record = failed_result.record
                print(f"{self._colorize(f'超限记录 {i}:', Colors.BRIGHT_YELLOW + Colors.BOLD)}")
                print(f"  - 客户名称: {self._colorize(record.customer_name, Colors.BRIGHT_WHITE)}")
                print(f"  - 客户手机号: {record.customer_phone}")

                for error_msg in failed_result.error_messages:
                    print(f"  - {self._colorize('超限详情:', Colors.BRIGHT_RED)} {error_msg}")
                print()

        print(f"{self._colorize(separator, Colors.BRIGHT_MAGENTA)}")

# 全局格式化器实例
console = ConsoleFormatter()

# 便捷函数
def print_banner(title: str, width: int = 80):
    """打印横幅"""
    console.print_banner(title, width)

def print_section(section_name: str):
    """打印章节"""
    console.print_section(section_name)

def print_step(step_num: int, description: str, total_steps: Optional[int] = None):
    """打印步骤"""
    console.print_step(step_num, description, total_steps)

def print_module_action(module_name: str, action: str, status: str = "", details: str = ""):
    """打印模块操作"""
    console.print_module_action(module_name, action, status, details)

def print_result(success: bool, message: str, details: Optional[str] = None):
    """打印结果"""
    console.print_result(success, message, details)

def print_summary(title: str, items: dict):
    """打印摘要"""
    console.print_summary(title, items)

def print_timestamp(prefix: str = "时间: "):
    """打印时间戳"""
    console.print_timestamp(prefix)

def print_conflict_detection_result(has_conflict: bool, conflicts: list):
    """打印数据冲突检测结果"""
    console.print_conflict_detection_result(has_conflict, conflicts)

def print_limit_validation_result(success: bool, failed_results: list):
    """打印金豆限额校验结果"""
    console.print_limit_validation_result(success, failed_results)
