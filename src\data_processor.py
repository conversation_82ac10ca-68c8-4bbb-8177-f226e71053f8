#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理引擎模块
实现数据清洗、转换、去重功能，支持批量文件读取和格式校验
"""

import pandas as pd
import numpy as np
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from src.utils import (
    load_config,
    find_files_by_pattern,
    standardize_date,
    validate_phone_number,
    validate_amount,
    clean_string,
    retry_operation
)
from src.logger import get_logger, log_data_processing, log_error
from src.models import ApplicationRecord, DataFrameConverter
from src.log_symbols import (
    LogSymbols, log_file_operation, log_data_processing as log_data_proc,
    log_workflow, log_problem
)
from src.problem_reporter import ProblemReporter, create_problem_reporter

class DataProcessor:
    """数据处理引擎"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化数据处理引擎
        
        Args:
            config_path: 配置文件路径
        """
        self.config = load_config(config_path)
        self.logger = get_logger("data_processor")

        # 初始化问题报告器
        self.problem_reporter = create_problem_reporter(self.logger)

        # 获取配置参数
        self.data_paths = self.config.get('data_paths', {})
        self.data_files = self.config.get('data_files', {})
        self.processing_config = self.config.get('data_processing', {})
        
    def find_application_detail_files(self) -> List[str]:
        """
        查找所有金豆发放申请明细表文件

        自动过滤Excel临时文件（以~$开头的文件）以避免权限错误

        Returns:
            文件路径列表
        """
        try:
            raw_data_dir = self.data_paths.get('raw_data_dir', 'Data/Raw')
            pattern = self.data_files.get('application_detail_pattern', '金豆发放申请明细表')

            files = find_files_by_pattern(raw_data_dir, pattern)

            # 过滤Excel文件并排除临时文件
            excel_files = []
            temp_files_skipped = 0

            for f in files:
                if f.endswith(('.xlsx', '.xls')):
                    # 获取文件名（不包含路径）
                    filename = os.path.basename(f)

                    # 跳过Excel临时文件（以~$开头）
                    if filename.startswith('~$'):
                        temp_files_skipped += 1
                        self.logger.debug(f"跳过Excel临时文件: {filename}")
                        continue

                    excel_files.append(f)

            # 记录处理结果
            if temp_files_skipped > 0:
                self.logger.info(f"自动跳过 {temp_files_skipped} 个Excel临时文件")

            # 使用符号标识的日志输出
            log_file_operation(self.logger, "search", "申请明细表文件", f"找到 {len(excel_files)} 个文件")

            return excel_files

        except Exception as e:
            log_error("查找申请明细表文件失败", e)
            return []
    
    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """
        读取Excel文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame
        """
        try:
            # 使用重试机制读取文件
            df = retry_operation(
                func=pd.read_excel,
                max_attempts=3,
                delay_seconds=1,
                args=[file_path]
            )
            
            # 优化2：使用符号标识的日志输出
            log_file_operation(self.logger, "read", os.path.basename(file_path), f"成功读取 {len(df)} 行数据")
            return df
        
        except Exception as e:
            log_error(f"读取Excel文件失败: {file_path}", e)
            return pd.DataFrame()
    
    def clean_and_validate_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        清洗和验证数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            (清洗后的DataFrame, 错误信息列表)
        """
        errors = []
        cleaned_df = df.copy()
        
        try:
            # 1. 清洗字符串字段（去除前后空格）
            string_columns = ['客户名称', '申请发放机构', '客户归属企业']
            for col in string_columns:
                if col in cleaned_df.columns:
                    cleaned_df[col] = cleaned_df[col].apply(clean_string)
            
            # 2. 标准化日期格式
            # 处理申请发放日期
            if '申请发放日期' in cleaned_df.columns:
                cleaned_df['申请发放日期'] = cleaned_df['申请发放日期'].apply(standardize_date)

                # 检查申请发放日期格式
                invalid_dates = cleaned_df[cleaned_df['申请发放日期'] == '']
                if len(invalid_dates) > 0:
                    errors.append(f"发现 {len(invalid_dates)} 条记录的申请发放日期格式无效")
                    # 记录具体的无效日期
                    for idx, row in invalid_dates.iterrows():
                        errors.append(f"  行 {idx+1}: 客户 {row.get('客户名称', 'N/A')} 申请发放日期 {row.get('申请发放日期', 'N/A')} 格式无效")

            # 处理申请填报日期
            if '申请填报日期' in cleaned_df.columns:
                cleaned_df['申请填报日期'] = cleaned_df['申请填报日期'].apply(standardize_date)

                # 检查申请填报日期格式
                invalid_report_dates = cleaned_df[cleaned_df['申请填报日期'] == '']
                if len(invalid_report_dates) > 0:
                    errors.append(f"发现 {len(invalid_report_dates)} 条记录的申请填报日期格式无效")
                    # 记录具体的无效日期
                    for idx, row in invalid_report_dates.iterrows():
                        errors.append(f"  行 {idx+1}: 客户 {row.get('客户名称', 'N/A')} 申请填报日期 {row.get('申请填报日期', 'N/A')} 格式无效")

                # 验证申请填报日期的有效性（不能晚于申请发放日期）
                if '申请发放日期' in cleaned_df.columns:
                    # 只对两个日期都有效的记录进行比较
                    valid_dates_mask = (cleaned_df['申请发放日期'] != '') & (cleaned_df['申请填报日期'] != '')
                    valid_dates_df = cleaned_df[valid_dates_mask]

                    if len(valid_dates_df) > 0:
                        # 转换为日期格式进行比较
                        try:
                            valid_dates_df_copy = valid_dates_df.copy()
                            valid_dates_df_copy['申请发放日期_dt'] = pd.to_datetime(valid_dates_df_copy['申请发放日期'], errors='coerce')
                            valid_dates_df_copy['申请填报日期_dt'] = pd.to_datetime(valid_dates_df_copy['申请填报日期'], errors='coerce')

                            # 检查申请填报日期是否晚于申请发放日期
                            invalid_sequence = valid_dates_df_copy[
                                valid_dates_df_copy['申请填报日期_dt'] > valid_dates_df_copy['申请发放日期_dt']
                            ]

                            if len(invalid_sequence) > 0:
                                errors.append(f"发现 {len(invalid_sequence)} 条记录的申请填报日期晚于申请发放日期")
                                # 记录具体的无效记录
                                for idx, row in invalid_sequence.iterrows():
                                    errors.append(f"  行 {idx+1}: 客户 {row.get('客户名称', 'N/A')} 填报日期 {row.get('申请填报日期', 'N/A')} 晚于发放日期 {row.get('申请发放日期', 'N/A')}")

                        except Exception as date_compare_error:
                            errors.append(f"日期有效性验证失败: {str(date_compare_error)}")

            # 3. 验证客户归属企业的条件必填逻辑
            if '归属条线' in cleaned_df.columns and '客户归属企业' in cleaned_df.columns:
                # 检查归属条线为"公司"时客户归属企业是否为空
                company_line_mask = cleaned_df['归属条线'] == '公司'
                company_records = cleaned_df[company_line_mask]

                if len(company_records) > 0:
                    empty_enterprise = company_records[
                        (company_records['客户归属企业'].isnull()) |
                        (company_records['客户归属企业'].astype(str).str.strip() == '')
                    ]

                    if len(empty_enterprise) > 0:
                        errors.append(f"发现 {len(empty_enterprise)} 条归属条线为'公司'但客户归属企业为空的记录")
                        # 记录具体的无效记录
                        for idx, row in empty_enterprise.iterrows():
                            errors.append(f"  行 {idx+1}: 客户 {row.get('客户名称', 'N/A')} 归属条线为'公司'但客户归属企业为空")
            
            # 4. 验证手机号格式
            if '客户手机号' in cleaned_df.columns:
                # 转换为字符串格式进行验证
                cleaned_df['客户手机号_str'] = cleaned_df['客户手机号'].astype(str)
                invalid_phones = cleaned_df[~cleaned_df['客户手机号_str'].apply(validate_phone_number)]
                
                if len(invalid_phones) > 0:
                    errors.append(f"发现 {len(invalid_phones)} 条记录的客户手机号格式无效")
                    # 记录具体的无效手机号
                    for idx, row in invalid_phones.iterrows():
                        errors.append(f"  行 {idx+1}: 客户 {row.get('客户名称', 'N/A')} 手机号 {row.get('客户手机号', 'N/A')} 格式无效")
                
                # 删除临时列
                cleaned_df = cleaned_df.drop('客户手机号_str', axis=1)
            
            # 5. 验证金豆发放数量格式
            if '等值金豆发放数量' in cleaned_df.columns:
                invalid_amounts = cleaned_df[~cleaned_df['等值金豆发放数量'].apply(validate_amount)]
                
                if len(invalid_amounts) > 0:
                    errors.append(f"发现 {len(invalid_amounts)} 条记录的等值金豆发放数量无效")
                    # 记录具体的无效金额
                    for idx, row in invalid_amounts.iterrows():
                        errors.append(f"  行 {idx+1}: 客户 {row.get('客户名称', 'N/A')} 发放数量 {row.get('等值金豆发放数量', 'N/A')} 无效")
            
            # 6. 检查必填字段
            required_fields = ['客户名称', '客户手机号', '等值金豆发放数量', '申请发放机构', '申请发放日期', '有效时间（天）', '活动方案全称', '归属条线', '申请填报日期']
            for field in required_fields:
                if field in cleaned_df.columns:
                    null_count = cleaned_df[field].isnull().sum()
                    if null_count > 0:
                        errors.append(f"必填字段 {field} 有 {null_count} 条空值记录")
            
            log_data_processing("数据清洗", f"完成数据清洗，发现 {len(errors)} 个问题", len(cleaned_df))
            
            return cleaned_df, errors

        except Exception as e:
            log_error("数据清洗失败", e)
            return df, [f"数据清洗过程中发生错误: {str(e)}"]

    def detect_data_conflicts(self, new_df: pd.DataFrame, main_detail_df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        检测数据整合冲突（性能优化版本）

        基于组合主键 (客户名称, 客户手机号, 客户归属企业) 进行比对，
        检查是否存在相同手机号和客户归属企业但客户名称不同，
        或相同客户名称和客户归属企业但手机号不同的情况

        新户识别逻辑：
        - 当客户归属企业字段为空值（None、空字符串、NaN）或不同时，
          即使客户名称相同也应视为不同客户（新户），不触发冲突检测逻辑
        - 空值企业名称也能正确参与冲突检测逻辑，空值被视为一个有效的企业归属值

        性能优化措施：
        1. 使用哈希表提高查找效率
        2. 分批处理大数据量
        3. 添加性能监控
        4. 优化内存使用

        Args:
            new_df: 新的申请明细数据
            main_detail_df: 主明细表数据

        Returns:
            (是否有冲突, 冲突详情列表)
        """
        import time
        start_time = time.time()

        try:
            conflicts = []
            has_conflict = False

            if len(main_detail_df) == 0:
                self.logger.debug("主明细表为空，跳过冲突检测")
                return False, []

            # 性能监控：记录数据量
            new_count = len(new_df)
            main_count = len(main_detail_df)
            self.logger.info(f"冲突检测开始：新数据 {new_count} 条，主明细表 {main_count} 条")

            # 数据量阈值检查
            LARGE_DATA_THRESHOLD = 10000
            if new_count > LARGE_DATA_THRESHOLD or main_count > LARGE_DATA_THRESHOLD:
                self.logger.warning(f"数据量较大，可能影响性能：新数据 {new_count} 条，主明细表 {main_count} 条")

            # 标准化数据，处理空值
            def normalize_enterprise(value):
                """
                标准化企业名称，确保空值能正确参与比较

                Args:
                    value: 原始企业名称值

                Returns:
                    标准化后的企业名称，空值统一为空字符串
                """
                # 下面这句语法意思是 如果value是NaN或者None，则返回空字符串
                if pd.isna(value) or value is None:
                    return ""
                # .strip()指去字符串两端的空格
                value_str = str(value).strip()
                 # 下面这句语法意思是 如果value_str为空字符串或者value_str的小写形式等于'nan'，则返回空字符串
                if value_str == '' or value_str.lower() == 'nan':
                    return ""
                return value_str

            # 使用向量化操作优化数据预处理
            def build_hash_maps(df, data_source=""):
                """
                构建哈希映射表，优化查找性能

                Args:
                    df: 数据框
                    data_source: 数据源标识（用于调试）

                Returns:
                    (phone_enterprise_map, name_enterprise_map): 手机号+企业映射表, 姓名+企业映射表
                """
                # 向量化处理，避免逐行迭代
                df_copy = df.copy()
                df_copy['客户名称_norm'] = df_copy['客户名称'].astype(str).str.strip()
                df_copy['客户手机号_norm'] = df_copy['客户手机号'].astype(str).str.strip()
                df_copy['客户归属企业_norm'] = df_copy['客户归属企业'].apply(normalize_enterprise)

                # 构建组合键（确保空值企业名称能正确参与比较）
                df_copy['phone_enterprise_key'] = df_copy['客户手机号_norm'] + '_' + df_copy['客户归属企业_norm']
                df_copy['name_enterprise_key'] = df_copy['客户名称_norm'] + '_' + df_copy['客户归属企业_norm']

                # 构建哈希映射
                phone_enterprise_map = {}
                name_enterprise_map = {}

                for _, row in df_copy.iterrows():
                    pe_key = row['phone_enterprise_key']
                    ne_key = row['name_enterprise_key']

                    record_info = {
                        'name': row['客户名称_norm'],
                        'phone': row['客户手机号_norm'],
                        'enterprise': row['客户归属企业_norm']
                    }

                    # 使用字典的get方法避免重复检查
                    if pe_key not in phone_enterprise_map:
                        phone_enterprise_map[pe_key] = record_info
                    if ne_key not in name_enterprise_map:
                        name_enterprise_map[ne_key] = record_info

                # 调试信息
                self.logger.debug(f"{data_source} 数据映射构建完成：手机号+企业组合 {len(phone_enterprise_map)} 个，姓名+企业组合 {len(name_enterprise_map)} 个")

                return phone_enterprise_map, name_enterprise_map

            # 构建新数据的哈希映射
            new_pe_map, new_ne_map = build_hash_maps(new_df, "申请明细表")

            # 构建主明细表的哈希映射
            main_pe_map, main_ne_map = build_hash_maps(main_detail_df, "主明细表")

            # 使用优化的哈希表进行冲突检测
            conflict_check_start = time.time()

            # 扩展冲突检测范围 - 首先检测申请明细表内部冲突
            internal_conflicts = self._detect_internal_conflicts(new_df)
            if internal_conflicts:
                has_conflict = True
                conflicts.extend(internal_conflicts)

            # 检测冲突：手机号和企业相同，但客户名称不同
            for pe_key, new_data in new_pe_map.items():
                if pe_key in main_pe_map:
                    main_data = main_pe_map[pe_key]
                    if new_data['name'] != main_data['name']:
                        has_conflict = True
                        enterprise_display = new_data['enterprise'] if new_data['enterprise'] else '[空]'
                        conflicts.append(
                            f"手机号和企业相同，但客户名称不同|手机号:{new_data['phone']}|企业:{enterprise_display}|"
                            f"申请明细表客户名称:{new_data['name']}|主明细表客户名称:{main_data['name']}"
                        )

            # 检测冲突：客户名称和企业相同，但手机号不同
            for ne_key, new_data in new_ne_map.items():
                if ne_key in main_ne_map:
                    main_data = main_ne_map[ne_key]
                    if new_data['phone'] != main_data['phone']:
                        has_conflict = True
                        enterprise_display = new_data['enterprise'] if new_data['enterprise'] else '[空]'
                        conflicts.append(
                            f"客户名称和企业相同，但手机号不同|客户:{new_data['name']}|企业:{enterprise_display}|"
                            f"申请明细表手机号:{new_data['phone']}|主明细表手机号:{main_data['phone']}"
                        )

            conflict_check_time = time.time() - conflict_check_start
            total_time = time.time() - start_time

            # 性能监控日志
            self.logger.info(f"冲突检测性能统计：")
            self.logger.info(f"  - 总耗时: {total_time:.3f}秒")
            self.logger.info(f"  - 冲突检查耗时: {conflict_check_time:.3f}秒")
            self.logger.info(f"  - 处理速度: {(new_count + main_count) / total_time:.0f} 条/秒")

            if has_conflict:
                log_error("数据整合冲突检测", f"发现 {len(conflicts)} 个冲突")

                # 格式化冲突输出
                formatted_conflicts = []
                for i, conflict in enumerate(conflicts, 1):
                    parts = conflict.split('|')
                    if len(parts) >= 5:
                        conflict_type = parts[0]
                        phone_or_name = parts[1]
                        enterprise = parts[2]
                        detail1 = parts[3]
                        detail2 = parts[4]

                        formatted_conflict = f"""
                            冲突记录 {i}:
                            - 冲突类型: {conflict_type}
                            - {phone_or_name}
                            - {enterprise}
                            - {detail1}
                            - {detail2}"""
                        formatted_conflicts.append(formatted_conflict)
                        self.logger.error(formatted_conflict)
                    else:
                        # 兼容旧格式
                        self.logger.error(f"冲突详情: {conflict}")
                        formatted_conflicts.append(conflict)

                return has_conflict, formatted_conflicts
            else:
                log_data_processing("冲突检测", "未发现数据冲突", 0)

            return has_conflict, conflicts

        except Exception as e:
            total_time = time.time() - start_time
            self.logger.error(f"冲突检测失败，耗时: {total_time:.3f}秒")
            log_error("冲突检测失败", e)
            return True, [f"冲突检测过程中发生错误: {str(e)}"]

    def _detect_internal_conflicts(self, df: pd.DataFrame) -> List[str]:
        """
        检测申请明细表内部记录之间的冲突

        Args:
            df: 申请明细表数据框

        Returns:
            冲突信息列表
        """
        conflicts = []

        try:
            def normalize_enterprise(value):
                """标准化企业名称"""
                if pd.isna(value) or value is None:
                    return ""
                value_str = str(value).strip()
                if value_str == '' or value_str.lower() == 'nan':
                    return ""
                return value_str

            # 标准化数据
            df_copy = df.copy()
            df_copy['客户名称_norm'] = df_copy['客户名称'].astype(str).str.strip()
            df_copy['客户手机号_norm'] = df_copy['客户手机号'].astype(str).str.strip()
            df_copy['客户归属企业_norm'] = df_copy['客户归属企业'].apply(normalize_enterprise)

            # 构建组合键映射
            phone_enterprise_records = {}  # 手机号+企业 -> 客户名称列表
            name_enterprise_records = {}   # 客户名称+企业 -> 手机号列表

            for idx, row in df_copy.iterrows():
                pe_key = f"{row['客户手机号_norm']}_{row['客户归属企业_norm']}"
                ne_key = f"{row['客户名称_norm']}_{row['客户归属企业_norm']}"

                # 记录手机号+企业对应的客户名称
                if pe_key not in phone_enterprise_records:
                    phone_enterprise_records[pe_key] = set()
                phone_enterprise_records[pe_key].add(row['客户名称_norm'])

                # 记录客户名称+企业对应的手机号
                if ne_key not in name_enterprise_records:
                    name_enterprise_records[ne_key] = set()
                name_enterprise_records[ne_key].add(row['客户手机号_norm'])

            # 检测冲突：相同手机号+企业，但客户名称不同
            for pe_key, names in phone_enterprise_records.items():
                if len(names) > 1:
                    phone, enterprise = pe_key.split('_', 1)
                    enterprise_display = enterprise if enterprise else '[空]'
                    names_list = list(names)
                    conflicts.append(
                        f"申请明细表内部冲突-相同手机号不同客户名称|手机号:{phone}|企业:{enterprise_display}|"
                        f"客户名称:{', '.join(names_list)}"
                    )

            # 检测冲突：相同客户名称+企业，但手机号不同
            for ne_key, phones in name_enterprise_records.items():
                if len(phones) > 1:
                    name, enterprise = ne_key.split('_', 1)
                    enterprise_display = enterprise if enterprise else '[空]'
                    phones_list = list(phones)
                    conflicts.append(
                        f"申请明细表内部冲突-相同客户名称不同手机号|客户:{name}|企业:{enterprise_display}|"
                        f"手机号:{', '.join(phones_list)}"
                    )

            if conflicts:
                log_data_proc(self.logger, "内部冲突检测", f"发现 {len(conflicts)} 个内部冲突")
            else:
                log_data_proc(self.logger, "内部冲突检测", "未发现内部冲突")

            return conflicts

        except Exception as e:
            log_error("申请明细表内部冲突检测失败", e)
            return []

    def remove_duplicates(self, df: pd.DataFrame, main_detail_df: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """
        去除重复记录（基于优化的业务主键）

        业务主键定义：客户名称 + 客户手机号 + 申请发放日期 + 客户归属企业 + 申请发放机构 + 等值金豆发放数量

        Args:
            df: 新的申请明细数据
            main_detail_df: 主明细表数据

        Returns:
            (去重后的DataFrame, 重复记录数)
        """
        try:
            # 创建优化的业务主键（处理空值情况）
            def create_business_key(row):
                # 标准化处理空值
                customer_name = str(row.get('客户名称', '')).strip()
                customer_phone = str(row.get('客户手机号', '')).strip()
                application_date = str(row.get('申请发放日期', '')).strip()
                organization = str(row.get('申请发放机构', '')).strip()
                amount = str(row.get('等值金豆发放数量', '')).strip()

                # 处理客户归属企业的空值
                customer_enterprise = row.get('客户归属企业')
                if pd.isna(customer_enterprise) or str(customer_enterprise).strip() == '':
                    customer_enterprise = ""
                else:
                    customer_enterprise = str(customer_enterprise).strip()

                return f"{customer_name}_{customer_phone}_{application_date}_{customer_enterprise}_{organization}_{amount}"

            # 为新数据创建业务主键
            df_with_key = df.copy()
            df_with_key['business_key'] = df_with_key.apply(create_business_key, axis=1)

            # 记录业务主键创建详情（用于调试）
            self.logger.debug(f"新数据业务主键示例: {df_with_key['business_key'].head(3).tolist()}")

            # 为主明细表数据创建业务主键
            main_detail_with_key = main_detail_df.copy()
            if len(main_detail_with_key) > 0:
                # 先标准化主明细表的日期格式
                main_detail_with_key['申请发放日期'] = main_detail_with_key['申请发放日期'].apply(standardize_date)
                main_detail_with_key['business_key'] = main_detail_with_key.apply(create_business_key, axis=1)

                # 获取已存在的业务主键
                existing_keys = set(main_detail_with_key['business_key'].tolist())
                self.logger.debug(f"主明细表业务主键数量: {len(existing_keys)}")
            else:
                existing_keys = set()
                self.logger.debug("主明细表为空，无现有业务主键")

            # 筛选出不重复的记录
            original_count = len(df_with_key)
            unique_df = df_with_key[~df_with_key['business_key'].isin(existing_keys)]
            duplicate_count = original_count - len(unique_df)

            # 记录重复的业务主键（用于调试）
            if duplicate_count > 0:
                duplicate_keys = df_with_key[df_with_key['business_key'].isin(existing_keys)]['business_key'].tolist()
                self.logger.info(f"发现重复的业务主键: {duplicate_keys[:5]}...")  # 只显示前5个

            # 删除临时的业务主键列
            unique_df = unique_df.drop('business_key', axis=1)

            log_data_processing("去重处理", f"原始记录 {original_count} 条，去重后 {len(unique_df)} 条，重复 {duplicate_count} 条", len(unique_df))

            return unique_df, duplicate_count

        except Exception as e:
            log_error("去重处理失败", e)
            return df, 0
    
    def batch_process_files(self, file_paths: List[str]) -> Tuple[pd.DataFrame, List[str]]:
        """
        批量处理多个文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            (合并后的DataFrame, 错误信息列表)
        """
        all_data = []
        all_errors = []
        
        try:
            for file_path in file_paths:
                self.logger.info(f"处理文件: {os.path.basename(file_path)}")
                
                # 读取文件
                df = self.read_excel_file(file_path)
                if df.empty:
                    all_errors.append(f"文件 {os.path.basename(file_path)} 读取失败或为空")
                    continue
                
                # 清洗和验证数据
                cleaned_df, errors = self.clean_and_validate_data(df)
                all_errors.extend([f"{os.path.basename(file_path)}: {error}" for error in errors])
                
                # 如果有严重错误，跳过此文件
                if errors and any("格式无效" in error or "无效" in error for error in errors):
                    all_errors.append(f"文件 {os.path.basename(file_path)} 包含严重数据错误，跳过处理")
                    continue
                
                all_data.append(cleaned_df)
            
            # 合并所有数据
            if all_data:
                combined_df = pd.concat(all_data, ignore_index=True)
                log_data_processing("批量处理", f"成功处理 {len(file_paths)} 个文件", len(combined_df))
            else:
                combined_df = pd.DataFrame()
                all_errors.append("没有成功处理任何文件")
            
            return combined_df, all_errors
        
        except Exception as e:
            log_error("批量文件处理失败", e)
            return pd.DataFrame(), [f"批量处理过程中发生错误: {str(e)}"]
    
    def process_application_details(self) -> Tuple[List[ApplicationRecord], List[str]]:
        """
        处理金豆发放申请明细表
        
        Returns:
            (处理后的记录列表, 错误信息列表)
        """
        try:
            # 1. 查找所有申请明细表文件
            file_paths = self.find_application_detail_files()
            if not file_paths:
                return [], ["未找到任何金豆发放申请明细表文件"]
            
            # 2. 批量处理文件
            combined_df, errors = self.batch_process_files(file_paths)
            if combined_df.empty:
                return [], errors + ["没有可处理的数据"]
            
            # 3. 读取主明细表进行冲突检测和去重
            main_detail_path = os.path.join(
                self.data_paths.get('raw_data_dir', 'Data/Raw'),
                self.data_files.get('application_main_detail', '金豆发放申请主明细表.xlsx')
            )

            main_detail_df = self.read_excel_file(main_detail_path)

            # 4. 数据整合冲突检测
            has_conflict, conflict_details = self.detect_data_conflicts(combined_df, main_detail_df)

            # 使用优化的控制台输出格式显示冲突检测结果
            try:
                from src.console_output import print_conflict_detection_result
                print_conflict_detection_result(has_conflict, conflict_details)
            except ImportError:
                # 兼容性处理
                if has_conflict:
                    print(f"\n{'='*60}")
                    print("数据冲突检测结果")
                    print(f"{'='*60}")
                    print(f"✗ 发现 {len(conflict_details)} 个冲突记录")
                    for i, conflict in enumerate(conflict_details, 1):
                        print(f"冲突记录 {i}: {conflict}")
                    print(f"{'='*60}")

            if has_conflict:
                # 发现冲突时阻断整合流程及所有后续处理
                error_msg = "数据整合冲突检测失败，流程已阻断！"
                errors.append(error_msg)
                errors.append("冲突详情：")
                errors.extend(conflict_details)

                # 记录详细的冲突信息到日志
                self.logger.error("=" * 80)
                self.logger.error("数据整合冲突检测失败 - 流程阻断")
                self.logger.error("=" * 80)
                for detail in conflict_details:
                    self.logger.error(f"冲突: {detail}")
                self.logger.error("=" * 80)

                # 立即返回，阻断所有后续处理
                return [], errors

            # 5. 去重处理（仅在无冲突时执行）
            unique_df, duplicate_count = self.remove_duplicates(combined_df, main_detail_df)

            if duplicate_count > 0:
                errors.append(f"发现并跳过 {duplicate_count} 条重复记录")

            # 6. 转换为ApplicationRecord对象
            records = DataFrameConverter.to_application_records(unique_df)
            
            log_data_processing("申请明细处理", f"最终处理完成，得到 {len(records)} 条有效记录", len(records))
            
            return records, errors
        
        except Exception as e:
            log_error("处理申请明细表失败", e)
            return [], [f"处理申请明细表时发生错误: {str(e)}"]

# 测试函数
def test_data_processor():
    """测试数据处理引擎"""
    print("测试数据处理引擎...")
    
    processor = DataProcessor()
    
    # 测试查找文件
    files = processor.find_application_detail_files()
    print(f"找到文件: {files}")
    
    # 测试处理申请明细
    records, errors = processor.process_application_details()
    print(f"处理结果: {len(records)} 条记录")
    if errors:
        print(f"错误信息: {errors}")
    
    print("数据处理引擎测试完成")

if __name__ == "__main__":
    test_data_processor()
