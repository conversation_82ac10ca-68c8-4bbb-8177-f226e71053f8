#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据问题详细报告模块
优化4：提供问题统计、问题详情和日志友好性，确保问题报告易于阅读和理解
"""

import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from src.log_symbols import LogSymbols, log_problem

@dataclass
class DataProblem:
    """数据问题信息"""
    problem_type: str           # 问题类型
    file_name: str             # 文件名
    row_number: Optional[int]   # 行号（可选）
    column_name: Optional[str]  # 字段名（可选）
    problem_value: str         # 问题值
    description: str           # 问题描述
    impact: str               # 影响说明
    suggestion: str           # 建议解决方案

class ProblemReporter:
    """数据问题报告器"""
    
    def __init__(self, logger):
        """
        初始化问题报告器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger
        self.problems = []  # 问题列表
        
    def add_problem(self, problem: DataProblem):
        """添加问题"""
        self.problems.append(problem)
        
    def add_format_problem(self, file_name: str, row_number: int, column_name: str, 
                          value: str, expected_format: str):
        """添加数据格式问题"""
        problem = DataProblem(
            problem_type="数据格式错误",
            file_name=file_name,
            row_number=row_number,
            column_name=column_name,
            problem_value=value,
            description=f"字段 '{column_name}' 的值 '{value}' 不符合预期格式",
            impact=f"该记录无法正确处理，期望格式：{expected_format}",
            suggestion="请检查数据源并修正格式错误"
        )
        self.add_problem(problem)
        
    def add_missing_field_problem(self, file_name: str, row_number: int, column_name: str):
        """添加必填字段缺失问题"""
        problem = DataProblem(
            problem_type="必填字段缺失",
            file_name=file_name,
            row_number=row_number,
            column_name=column_name,
            problem_value="[空值]",
            description=f"必填字段 '{column_name}' 为空",
            impact="该记录无法通过业务规则校验",
            suggestion="请补充必填字段的值"
        )
        self.add_problem(problem)
        
    def add_conflict_problem(self, file_name: str, conflict_type: str, details: str):
        """添加数据冲突问题"""
        problem = DataProblem(
            problem_type="数据冲突",
            file_name=file_name,
            row_number=None,
            column_name=None,
            problem_value=details,
            description=f"发现{conflict_type}冲突",
            impact="冲突记录无法整合到主明细表",
            suggestion="请检查并修正冲突的数据记录"
        )
        self.add_problem(problem)
        
    def add_validation_problem(self, file_name: str, row_number: int, 
                             validation_type: str, details: str):
        """添加校验失败问题"""
        problem = DataProblem(
            problem_type="校验失败",
            file_name=file_name,
            row_number=row_number,
            column_name=None,
            problem_value=details,
            description=f"{validation_type}校验失败",
            impact="该记录无法通过业务规则校验",
            suggestion="请检查记录是否符合业务规则要求"
        )
        self.add_problem(problem)
        
    def get_problems_by_file(self) -> Dict[str, List[DataProblem]]:
        """按文件分组获取问题"""
        problems_by_file = {}
        for problem in self.problems:
            file_name = problem.file_name
            if file_name not in problems_by_file:
                problems_by_file[file_name] = []
            problems_by_file[file_name].append(problem)
        return problems_by_file
        
    def get_problems_by_type(self) -> Dict[str, List[DataProblem]]:
        """按问题类型分组获取问题"""
        problems_by_type = {}
        for problem in self.problems:
            problem_type = problem.problem_type
            if problem_type not in problems_by_type:
                problems_by_type[problem_type] = []
            problems_by_type[problem_type].append(problem)
        return problems_by_type
        
    def generate_summary_report(self) -> str:
        """生成问题汇总报告"""
        if not self.problems:
            return "✅ 未发现数据问题"
            
        total_problems = len(self.problems)
        problems_by_file = self.get_problems_by_file()
        problems_by_type = self.get_problems_by_type()
        
        report_lines = []
        report_lines.append(f"🚨 数据问题汇总报告")
        report_lines.append("=" * 60)
        report_lines.append(f"总问题数量：{total_problems}")
        report_lines.append("")
        
        # 按文件统计
        report_lines.append("📁 按文件统计：")
        for file_name, file_problems in problems_by_file.items():
            report_lines.append(f"  • {file_name}：发现 {len(file_problems)} 个问题")
        report_lines.append("")
        
        # 按问题类型统计
        report_lines.append("📊 按问题类型统计：")
        for problem_type, type_problems in problems_by_type.items():
            report_lines.append(f"  • {problem_type}：{len(type_problems)} 个")
        report_lines.append("")
        
        return "\n".join(report_lines)
        
    def generate_detailed_report(self) -> str:
        """生成详细问题报告"""
        if not self.problems:
            return "✅ 未发现数据问题"
            
        report_lines = []
        report_lines.append("📄 数据问题详细报告")
        report_lines.append("=" * 80)
        
        problems_by_file = self.get_problems_by_file()
        
        for file_name, file_problems in problems_by_file.items():
            report_lines.append(f"\n📁 文件：{file_name}")
            report_lines.append("-" * 60)
            report_lines.append(f"问题数量：{len(file_problems)}")
            report_lines.append("")
            
            for i, problem in enumerate(file_problems, 1):
                report_lines.append(f"  问题 {i}：")
                report_lines.append(f"    类型：{problem.problem_type}")
                if problem.row_number:
                    report_lines.append(f"    行号：第 {problem.row_number} 行")
                if problem.column_name:
                    report_lines.append(f"    字段：{problem.column_name}")
                report_lines.append(f"    问题值：{problem.problem_value}")
                report_lines.append(f"    描述：{problem.description}")
                report_lines.append(f"    影响：{problem.impact}")
                report_lines.append(f"    建议：{problem.suggestion}")
                report_lines.append("")
                
        return "\n".join(report_lines)
        
    def log_problems(self):
        """记录问题到日志"""
        if not self.problems:
            log_problem(self.logger, "问题汇总", "未发现数据问题")
            return
            
        # 记录汇总信息
        summary = self.generate_summary_report()
        log_problem(self.logger, "问题汇总", f"发现 {len(self.problems)} 个数据问题")
        
        # 记录详细信息
        problems_by_file = self.get_problems_by_file()
        for file_name, file_problems in problems_by_file.items():
            log_problem(self.logger, "问题详情", 
                       f"{file_name} 发现 {len(file_problems)} 个问题",
                       f"问题类型：{', '.join(set(p.problem_type for p in file_problems))}")
            
            # 记录每个问题的详细信息
            for i, problem in enumerate(file_problems, 1):
                details = f"行号:{problem.row_number or 'N/A'} 字段:{problem.column_name or 'N/A'} 值:{problem.problem_value}"
                log_problem(self.logger, f"问题{i}", problem.description, details)
                
    def clear_problems(self):
        """清空问题列表"""
        self.problems.clear()
        
    def has_problems(self) -> bool:
        """是否有问题"""
        return len(self.problems) > 0
        
    def get_problem_count(self) -> int:
        """获取问题数量"""
        return len(self.problems)
        
    def get_file_problem_count(self, file_name: str) -> int:
        """获取指定文件的问题数量"""
        return len([p for p in self.problems if p.file_name == file_name])

def create_problem_reporter(logger) -> ProblemReporter:
    """创建问题报告器实例"""
    return ProblemReporter(logger)
