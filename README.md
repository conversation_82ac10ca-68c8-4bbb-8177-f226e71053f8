# 金豆发放监控与分析系统

## 项目概述和目标

本项目旨在构建一套完整的金豆发放监控与分析系统，通过自动化数据处理、规则校验和可视化呈现，实现以下三大核心目标：

- **业务合规监控**：实时、准确地识别金豆发放过程中违反业务规则的情况，确保发放行为符合规定
- **运营数据洞察**：通过多维度数据分析和交互式可视化看板，为运营决策提供数据支持，深入洞察客户行为与机构表现

## 功能列表及详细说明

### 1. 数据处理引擎
- **数据清洗与转换**：统一日期格式、去除空格、校验手机号和金豆数量格式
- **数据去重**：基于业务主键（客户名称+客户手机号+申请发放日期+等值金豆发放数量）进行去重
- **批量数据导入**：支持同时处理多个金豆发放申请明细表文件

### 2. 规则校验引擎
- **单次发放限制校验**：检查单次发放数量是否超过规定限额
- **年累计发放限制校验**：检查客户年度累计发放是否超过规定限额
- **户累计发放限制校验**：检查客户历史总累计发放是否超过规定限额
- **原子性处理**：批次中任何记录校验失败，整个批次都不会被导入

### 3. 数据统计与备份
- **自动数据备份**：处理前自动备份相关统计表
- **统计数据更新**：自动更新金豆发放统计表和年统计表
- **违规记录统计**：记录和统计各类违规情况

### 4. 交互式可视化看板
- **关键绩效指标展示**：金豆发放总额、发放总次数、违规次数等核心指标
- **多维度排行榜**：客户发放Top10、机构发放Top10、违规Top10等
- **趋势分析图表**：月度发放趋势、重点机构监控等
- **交互式筛选**：支持日期范围和机构筛选，图表联动更新

## 使用方法

### 基本使用流程
1. 将新的金豆发放申请明细表文件放置在 `Data/Raw` 目录下
2. 运行主程序：`python main.py`
3. 系统自动执行数据处理、规则校验和统计更新
4. 启动Web看板：`python app.py`
5. 在浏览器中访问 `http://localhost:5000` 查看分析结果

### 参数说明
- **数据文件命名**：支持包含"金豆发放申请明细表"关键词的任意文件名
- **日期格式**：系统自动将申请发放日期转换为YYYY-MM-DD格式
- **规则配置**：通过金豆发放规则配置表中的"是否生效"字段控制规则启用状态

### 返回值说明
- **成功处理**：返回导入成功的记录条数和更新的统计信息
- **校验失败**：返回详细的失败报告，包括失败记录信息和触发的具体规则
- **数据备份**：自动生成带时间戳的备份文件

## 安装和配置指南

### 环境要求
- Python 3.8+
- 操作系统：Windows/Linux/macOS

### 依赖安装
```bash
pip install pandas openpyxl flask plotly dash numpy scikit-learn
```

### 配置步骤
1. 确保所有数据表文件存放在 `Data/Raw` 目录下
2. 检查金豆发放规则配置表中的规则设置
3. 运行 `python main.py` 进行首次数据处理
4. 启动 `python app.py` 开启Web服务

## 项目结构
```
金豆发放/
├── Data/
│   ├── Raw/                    # 原始数据文件
│   ├── Processed/              # 处理后的数据文件
│   └── Backup/                 # 数据备份文件
├── Docs/                       # 项目文档
├── src/                        # 源代码
├── static/                     # 静态资源文件
├── templates/                  # HTML模板文件
├── logs/                       # 日志文件
├── main.py                     # 主程序入口
├── app.py                      # Web应用入口
└── config.yaml                 # 配置文件
```

## 技术栈
- **后端**：Python + Flask + pandas
- **前端**：ECharts + Bootstrap 5
- **数据处理**：pandas + openpyxl
- **可视化**：ECharts
