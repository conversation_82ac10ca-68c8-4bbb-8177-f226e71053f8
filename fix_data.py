#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def fix_record_ids():
    """修复记录ID重复问题"""
    try:
        # 读取主明细表，获取最大记录ID
        main_df = pd.read_excel('Data/Raw/金豆发放申请主明细表.xlsx')
        max_id = main_df['记录ID'].max()
        print(f"主明细表最大记录ID: {max_id}")
        
        # 读取申请明细表
        detail_df = pd.read_excel('Data/Raw/金豆发放申请明细表.xlsx')
        print(f"申请明细表原始记录ID: {detail_df['记录ID'].tolist()}")
        
        # 更新申请明细表的记录ID，从主明细表最大ID+1开始
        detail_df['记录ID'] = range(max_id + 1, max_id + 1 + len(detail_df))
        print(f"申请明细表新记录ID: {detail_df['记录ID'].tolist()}")
        
        # 保存更新后的申请明细表
        detail_df.to_excel('Data/Raw/金豆发放申请明细表.xlsx', index=False)
        print("申请明细表记录ID已更新")
        
    except Exception as e:
        print(f"修复记录ID时出错: {e}")

if __name__ == "__main__":
    fix_record_ids()
